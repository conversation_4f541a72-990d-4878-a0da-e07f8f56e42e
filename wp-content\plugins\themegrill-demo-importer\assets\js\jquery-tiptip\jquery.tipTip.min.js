!function(O){O.fn.tipTip=function(t){var g,b,M,w=O.extend({activation:"hover",keepAlive:!1,maxWidth:"200px",edgeOffset:3,defaultPosition:"bottom",delay:400,fadeIn:200,fadeOut:200,attribute:"title",content:!1,enter:function(){},exit:function(){}},t);return O("#tiptip_holder").length<=0?(g=O('<div id="tiptip_holder" style="max-width:'+w.maxWidth+';"></div>'),b=O('<div id="tiptip_content"></div>'),M=O('<div id="tiptip_arrow"></div>'),O("body").append(g.html(b).prepend(M.html('<div id="tiptip_arrow_inner"></div>')))):(g=O("#tiptip_holder"),b=O("#tiptip_content"),M=O("#tiptip_arrow")),this.each(function(){var _,m,v=O(this);function t(){w.enter.call(this),b.html(_),g.hide().removeAttr("class").css("margin","0"),M.removeAttr("style");var t=parseInt(v.offset().top),e=parseInt(v.offset().left),o=parseInt(v.outerWidth()),i=parseInt(v.outerHeight()),n=g.outerWidth(),r=g.outerHeight(),a=Math.round((o-n)/2),f=Math.round((i-r)/2),d=Math.round(e+a),u=Math.round(t+i+w.edgeOffset),p="",h="",l=Math.round(n-12)/2,c=("bottom"==w.defaultPosition?p="_bottom":"top"==w.defaultPosition?p="_top":"left"==w.defaultPosition?p="_left":"right"==w.defaultPosition&&(p="_right"),a+e<parseInt(O(window).scrollLeft())),s=n+e>parseInt(O(window).width()),o=(c&&a<0||"_right"==p&&!s||"_left"==p&&e<n+w.edgeOffset+5?(p="_right",h=Math.round(r-13)/2,l=-12,d=Math.round(e+o+w.edgeOffset),u=Math.round(t+f)):(s&&a<0||"_left"==p&&!c)&&(p="_left",h=Math.round(r-13)/2,l=Math.round(n),d=Math.round(e-(n+w.edgeOffset+5)),u=Math.round(t+f)),t+i+w.edgeOffset+r+8>parseInt(O(window).height()+O(window).scrollTop())),s=t+i-(w.edgeOffset+r+8)<0;o||"_bottom"==p&&o||"_top"==p&&!s?("_top"==p||"_bottom"==p?p="_top":p+="_top",h=r,u=Math.round(t-(r+5+w.edgeOffset))):(s|("_top"==p&&s)||"_bottom"==p&&!o)&&("_top"==p||"_bottom"==p?p="_bottom":p+="_bottom",h=-12,u=Math.round(t+i+w.edgeOffset)),"_right_top"==p||"_left_top"==p?u+=5:"_right_bottom"!=p&&"_left_bottom"!=p||(u-=5),"_left_top"!=p&&"_left_bottom"!=p||(d+=5),M.css({"margin-left":l+"px","margin-top":h+"px"}),g.css({"margin-left":d+"px","margin-top":u+"px"}).attr("class","tip"+p),m&&clearTimeout(m),m=setTimeout(function(){g.stop(!0,!0).fadeIn(w.fadeIn)},w.delay)}function e(){w.exit.call(this),m&&clearTimeout(m),g.fadeOut(w.fadeOut)}""!=(_=w.content||v.attr(w.attribute))&&(w.content||v.removeAttr(w.attribute),m=!1,"hover"==w.activation?(v.hover(function(){t()},function(){w.keepAlive||e()}),w.keepAlive&&g.hover(function(){},function(){e()})):"focus"==w.activation?v.focus(function(){t()}).blur(function(){e()}):"click"==w.activation&&(v.click(function(){return t(),!1}).hover(function(){},function(){w.keepAlive||e()}),w.keepAlive)&&g.hover(function(){},function(){e()}))})}}(jQuery);
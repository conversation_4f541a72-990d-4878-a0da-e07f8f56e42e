{"$schema": "https://schemas.wp.org/trunk/block.json", "name": "blockart/spacing", "title": "Spacing", "description": "Give your designs room to breathe with white space.", "keywords": ["spacing", "spacer", "divider"], "category": "blockart", "textdomain": "blockart", "supports": {"className": false, "customClassName": false}, "example": {"attributes": {}}, "attributes": {"clientId": {"type": "string"}, "height": {"type": "object", "default": {"desktop": {"value": 50, "unit": "px"}}, "style": [{"selector": "{{WRAPPER}} .blockart-spacing-inner { height: {{VALUE}}; }"}]}, "background": {"type": "object", "default": {"background": 1}, "style": [{"selector": "{{WRAPPER}} .blockart-spacing-inner"}]}, "hoverBackground": {"type": "object", "default": {"background": 1}, "style": [{"selector": "{{WRAPPER}} .blockart-spacing-inner:hover"}]}, "border": {"type": "object", "default": {"border": 1, "radius": {"desktop": {"lock": true}}, "size": {"desktop": {"lock": true}}}, "style": [{"selector": "{{WRAPPER}} .blockart-spacing-inner"}]}, "hoverBorder": {"type": "object", "default": {"border": 1, "radius": {"desktop": {"lock": true}}, "size": {"desktop": {"lock": true}}}, "style": [{"selector": "{{WRAPPER}} .blockart-spacing-inner:hover"}]}, "boxShadow": {"type": "object", "default": {"boxShadow": 1}, "style": [{"selector": "{{WRAPPER}} .blockart-spacing-inner"}]}, "boxShadowHover": {"type": "object", "default": {"boxShadow": 1}, "style": [{"selector": "{{WRAPPER}} .blockart-spacing-inner:hover"}]}, "blockMargin": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { margin: {{VALUE}}; }"}]}, "blockPadding": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { padding: {{VALUE}}; }"}]}, "blockZIndex": {"type": "number", "style": [{"selector": "{{WRAPPER}} { z-index: {{VALUE}}; }"}]}, "cssID": {"type": "string"}, "animation": {"type": "string"}, "interaction": {"type": "object"}, "position": {"type": "object"}, "hideOnDesktop": {"type": "boolean", "style": [{"selector": "@media (min-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnTablet": {"type": "boolean", "style": [{"selector": "@media (min-width:48em) and (max-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} { display: none; } }"}]}, "colReverseOnTablet": {"type": "boolean", "style": [{"selector": "@media (max-width:62em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "colReverseOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "blockCSS": {"type": "string"}, "className": {"type": "string"}}, "style": "blockart-blocks", "editorScript": "blockart-blocks", "editorStyle": "blockart-blocks-editor"}
<?php return array(
    'root' => array(
        'pretty_version' => '2.1.4',
        'version' => '2.1.4.0',
        'type' => 'wordpress-plugin',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => null,
        'name' => 'themegrill/blockart',
        'dev' => false,
    ),
    'versions' => array(
        'composer/installers' => array(
            'pretty_version' => 'v1.11.0',
            'version' => '1.11.0.0',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/./installers',
            'aliases' => array(),
            'reference' => 'ae03311f45dfe194412081526be2e003960df74b',
            'dev_requirement' => false,
        ),
        'halaxa/json-machine' => array(
            'pretty_version' => '1.2.5',
            'version' => '1.2.5.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../halaxa/json-machine',
            'aliases' => array(),
            'reference' => 'd0f84abf79ac98145d478b66d2bcf363d706477c',
            'dev_requirement' => false,
        ),
        'roundcube/plugin-installer' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'sabberworm/php-css-parser' => array(
            'pretty_version' => 'v8.9.0',
            'version' => '8.9.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sabberworm/php-css-parser',
            'aliases' => array(),
            'reference' => 'd8e916507b88e389e26d4ab03c904a082aa66bb9',
            'dev_requirement' => false,
        ),
        'shama/baton' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'themegrill/blockart' => array(
            'pretty_version' => '2.1.4',
            'version' => '2.1.4.0',
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => null,
            'dev_requirement' => false,
        ),
    ),
);

=== ThemeGrill Demo Importer ===
Contributors: ThemeGrill
Tags: themegrill, theme demos, demo, importer, one click import
Requires at least: 4.7
Tested up to: 6.8
Requires PHP: 7.4
Stable tag: 1.9.13
License: GPLv3 or later
License URI: https://www.gnu.org/licenses/gpl-3.0.html

Import ThemeGrill official themes demo content, widgets and theme settings with just one click.

== Description ==

Import <a href="https://themegrill.com/themes/" target="_blank" rel="nofollow">ThemeGrill</a> official themes demo content, widgets and theme settings with just one click.

Get [free support](https://themegrill.com/support-forum/)

== Notes ==

* The plugin makes a call to our CloudFront server remotely to import static demo content.

= Demo Importer in action: =

[youtube https://youtu.be/JgZbhBU1o4g]

= Requirements =

* WordPress 4.7 or later.
* [ThemeGrill Official Themes](https://themegrill.com/wordpress-themes/).

= Contribute =

You can contribute to the source code in our [GitHub](https://github.com/themegrill/themegrill-demo-importer/) page.

== Installation ==

1. Install the plugin either via the WordPress.org plugin directory, or by uploading the files to your server (in the /wp-content/plugins/ directory).
2. Activate the ThemeGrill Demo Importer plugin through the 'Plugins' menu in WordPress.

= Automatic installation =

Automatic installation is the easiest option as WordPress handles the file transfers itself and you don’t need to leave your web browser. To do an automatic install of ThemeGrill Demo Importer, log in to your WordPress dashboard, navigate to the Plugins menu and click Add New.

In the search field type “ThemeGrill Demo Importer” and click Search Plugins. Once you’ve found our demo importer plugin you can view details about it such as the the point release, rating and description. Most importantly of course, you can install it by simply clicking “Install Now”.

= Manual installation =

The manual installation method involves downloading our demo importer plugin and uploading it to your webserver via your favourite FTP application. The WordPress codex contains [instructions on how to do this here](https://codex.wordpress.org/Managing_Plugins#Manual_Plugin_Installation).

= Updating =

Automatic updates should work like a charm; as always though, ensure you backup your site just in case.

== Frequently Asked Questions ==

= What is the plugin license? =

* This plugin is released under a GPL license.

= What themes this plugin supports? =

* The plugin currently only supports ThemeGrill themes.

= Where can I report bugs or contribute to the project? =

Bugs can be reported either in our support forum or preferably on the [GitHub repository](https://github.com/themegrill/themegrill-demo-importer/issues).

= ThemeGrill Demo Importer is awesome! Can I contribute? =

Yes you can! Join in on our [GitHub repository](https://github.com/themegrill/themegrill-demo-importer/) :)

== Screenshots ==

1. Theme Demos listing page.
2. Install and activate the required plugins.
3. Finally, Import the Demo with just one click.

== Changelog ==
= 1.9.13 - 30-06-2025 =
* Fix   - Spacious widget import issue

= 1.9.12 - 13-05-2025 =
* Fix   - Colormag Footer builder menu 1 component import issue

= 1.9.11 - 13-05-2025 =
* Tweak   - Update WordPress tested upto version to 6.8.

= 1.9.10 - 13-05-2025 =
* Fix - Compatibility with magazine blocks.

= 1.9.9 - 21-11-2024 =
* Fix - Footer widget menu import issue.

= 1.9.8 - 16-10-2024 =
* Tweak - Update readme file.

= 1.9.7 - 29-05-2024 =
* Tweak - Remove compulsory companion elementor in Zakra premium demo.

= 1.9.6 - 16-05-2024 =
* Fix - Elementor kit import issue.

= 1.9.5 - 19-03-2024 =
* Tweak - Minor fixes.

= 1.9.4.4 - 15-01-2023 =
* Support - Support for theme dashboard enhancement.

= 1.9.4.3 - 04-12-2023 =
* Fix - Ornate Decor slug.

= 1.9.4.2 - 04-12-2023 =
* Fix - Ornate Decor config name.

= 1.9.4.1 - 03-12-2023 =
* Support – Support for Ornate Decor.
* Tweak   - Update WordPress tested upto version to 6.4.

= 1.9.4 - 23-08-2023 =
* Fix – Import fail message issue.

= 1.9.3 - 18-08-2023 =
* Support – Support for YITH Wishlist default settings when importing demo.
* Tweak   - Replace deprecated function get_page_by_title with WP_Query.

= 1.9.2.2 - 16-08-2023 =
* Support – Support for Kirana.
* Tweak - Update WordPress tested upto version to 6.3.

= 1.9.2.1 - 03-04-2023 =
* Fix – Support for Blissful.

= 1.9.2 - 03-04-2023 =
* Support – Support for Blissful.

= 1.9.1 - 15-02-2023 =
* Update - Stable tag.

= 1.9.0 - 15-02-2023 =
* Tweak – Update review notice message.
* Tweak - Updated WordPress tested upto version to 6.1.
* Tweak – Update popup information message.

= 1.8.9 - 14-12-2022 =
* Support – Support for Vastra.

= 1.8.8.2 - 07-11-2022 =
* Fix – Support for Gizmo.

= 1.8.8.1 - 07-11-2022 =
* Update – Stable tag.

= 1.8.8 - 07-11-2022 =
* Support – Support for Gizmo.

= 1.8.7 - 10-10-2022 =
* Update – Stable tag.

= 1.8.6 - 10-10-2022 =
* Remove  - Support for eStory Pro.

= 1.8.5 - 19-09-2022 =
* Fix - Compatibility with Masteriyo Pro.

= 1.8.4 - 31-08-2022 =
* Support - MagazineX theme.

= 1.8.3 - 14-07-2022 =
* Support - Libreria theme.

= 1.8.2 - 01-07-2022 =
* Support - eStory theme.

= 1.8.1 - 01-07-2022 =
- Fix    - Masteriyo page duplication.
- Update – Header field Tested up to.

= 1.8.0 - 08-03-2022 =
* Support - Skincare theme.
* Remove  - Support for Luza theme.

= 1.7.9 - 16-02-2022 =
* Tweak - Update static demo content url.

= 1.7.8 - 10-02-2022 =
* Support - Luza theme.

= 1.7.7 - 12-01-2022 =
* Support - BlockArt Blocks plugin.
* Support - Online Education theme.
* Fix     - PHP warning while importing widget.

= 1.7.6.1 - 31-12-2021 =
* Tweak - Update stable tag.

[See changelog for all versions](https://raw.githubusercontent.com/themegrill/themegrill-demo-importer/master/CHANGELOG.txt).

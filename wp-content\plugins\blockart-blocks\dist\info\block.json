{"apiVersion": 2, "$schema": "https://schemas.wp.org/trunk/block.json", "name": "blockart/info", "title": "Info Box", "description": "Redirect users to your important pages or websites by clicking button.", "keywords": ["info"], "category": "blockart", "textdomain": "blockart", "supports": {"className": false, "customClassName": false, "reusable": false}, "example": {"attributes": {}}, "attributes": {"clientId": {"type": "string"}, "headingText": {"type": "string", "default": "Title"}, "infoLayout": {"type": "string", "default": "layout-1"}, "headingToggle": {"type": "boolean", "default": true}, "markup": {"type": "string", "default": "h2"}, "alignment": {"type": "object", "style": [{"selector": "{{WRAPPER}}.blockart-info-box .blockart-info-container {text-align: {{VALUE}};} {{WRAPPER}}.blockart-info-box .blockart-info-container .blockart-info-content {text-align: {{VALUE}}; } {{WRAPPER}}.blockart-info-box .blockart-info-container .blockart-info-box-icon {text-align: {{VALUE}}; }"}]}, "headingTypography": {"type": "object", "default": {"typography": 1, "weight": 400}, "style": [{"selector": "{{WRAPPER}} .blockart-heading-wrapper .blockart-heading"}]}, "textTypography": {"type": "object", "default": {"typography": 1, "weight": 400}, "style": [{"selector": "{{WRAPPER}} .blockart-content-wrapper p"}]}, "buttonTypography": {"type": "object", "default": {"typography": 1, "weight": 400}, "style": [{"selector": "{{WRAPPER}} .blockart-button-wrapper .blockart-button"}]}, "headingColor": {"type": "string", "style": [{"selector": "{{WRAPPER}} .blockart-heading {color: {{VALUE}}; }"}]}, "textColor": {"type": "string", "style": [{"selector": "{{WRAPPER}} .blockart-content-wrapper p {color: {{VALUE}}; }"}]}, "buttonColor": {"type": "string", "default": "#FFFFFF", "style": [{"selector": "{{WRAPPER}} .blockart-button-wrapper .blockart-button {color: {{VALUE}}; }"}]}, "background": {"type": "object", "default": {"background": 1}, "style": [{"selector": "{{WRAPPER}}"}]}, "buttonBackground": {"type": "object", "default": {"background": 1, "type": "color", "color": "#2563eb"}, "style": [{"selector": "{{WRAPPER}} .blockart-button-wrapper .blockart-button"}]}, "textButton": {"type": "string", "default": "<PERSON><PERSON>"}, "urlPosition": {"type": "string", "default": "button-only"}, "width": {"type": "object", "style": [{"selector": "{{WRAPPER}} { flex-basis: {{VALUE}}%; width: {{VALUE}}%; }"}]}, "blockHTML": {"type": "string", "default": "div"}, "link": {"type": "object"}, "text": {"type": "string", "default": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. <PERSON><PERSON><PERSON> diam dolor, accumsan sed rutrum vel, dapibus et leo."}, "style": {"type": "string", "default": "filled"}, "buttonHover": {"type": "string", "default": "none"}, "buttonAnimation": {"type": "string", "default": "none"}, "size": {"type": "string", "default": "large"}, "padding": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"condition": [{"key": "size", "relation": "==", "value": "custom"}, {"key": "style", "relation": "!=", "value": "plain"}, {"key": "style", "relation": "!=", "value": "link"}], "selector": "{{WRAPPER}} .blockart-button-link.is-custom { padding: {{VALUE}}; }"}]}, "typography": {"type": "object", "default": {"typography": 1}, "style": [{"selector": "{{WRAPPER}} .blockart-button-link"}]}, "icon": {"type": "object", "default": {"enable": true, "icon": "star"}}, "iconPosition": {"type": "string", "default": "right"}, "iconSize": {"type": "object", "style": [{"selector": "{{WRAPPER}} .blockart-info-box-icon .blockart-icon { width: {{VALUE}}; height: auto; }"}]}, "iconBorder": {"type": "object", "default": {"border": 1, "radius": {"desktop": {"lock": true}}, "size": {"desktop": {"lock": true}}}, "style": [{"selector": "{{WRAPPER}} .blockart-info-container .blockart-info-box-icon-wrapper .blockart-info-box-icon"}]}, "buttonBorder": {"type": "object", "default": {"border": 1, "radius": {"desktop": {"lock": true}}, "size": {"desktop": {"lock": true}}}, "style": [{"selector": "{{WRAPPER}} .blockart-button-wrapper .blockart-button"}]}, "infoBorder": {"type": "object", "default": {"border": 1, "radius": {"desktop": {"lock": true}}, "size": {"desktop": {"lock": true}}}, "style": [{"selector": "{{WRAPPER}}.blockart-info-box"}]}, "color1": {"type": "string", "default": "#fff", "style": [{"condition": [{"key": "style", "relation": "==", "value": "filled"}], "selector": "{{WRAPPER}} .blockart-button-link { color: {{VALUE}}; }{{WRAPPER}} .blockart-button-icon .blockart-icon { fill: {{VALUE}}; }"}]}, "color2": {"type": "string", "default": "#2563EB", "style": [{"condition": [{"key": "style", "relation": "!=", "value": "filled"}], "selector": "{{WRAPPER}} .blockart-button-link{ color: {{VALUE}}; }{{WRAPPER}} .blockart-button-icon .blockart-icon { fill: {{VALUE}}; }"}]}, "hoverColor1": {"type": "string", "default": "", "style": [{"condition": [{"key": "style", "relation": "==", "value": "filled"}], "selector": "{{WRAPPER}} .blockart-button-link:hover { color: {{VALUE}} }{{WRAPPER}} .blockart-button-link:hover .blockart-button-icon .blockart-icon { fill: {{VALUE}} }{{WRAPPER}} .blockart-button-link:focus { color: {{VALUE}} }{{WRAPPER}} .blockart-button-link:focus .blockart-button-icon .blockart-icon { fill: {{VALUE}} }{{WRAPPER}} .blockart-button-link:active { color: {{VALUE}} }{{WRAPPER}} .blockart-button-link:active .blockart-button-icon .blockart-icon { fill: {{VALUE}} }"}]}, "hoverColor2": {"type": "string", "default": "", "style": [{"condition": [{"key": "style", "relation": "!=", "value": "filled"}], "selector": "{{WRAPPER}} .blockart-button-link:hover { color: {{VALUE}} }{{WRAPPER}} .blockart-button-link:hover .blockart-button-icon .blockart-icon { fill: {{VALUE}} }{{WRAPPER}} .blockart-button-link:focus { color: {{VALUE}} }{{WRAPPER}} .blockart-button-link:focus .blockart-button-icon .blockart-icon { fill: {{VALUE}} }{{WRAPPER}} .blockart-button-link:active { color: {{VALUE}} }{{WRAPPER}} .blockart-button-link:active .blockart-button-icon .blockart-icon { fill: {{VALUE}} }"}]}, "background1": {"type": "object", "default": {"background": 1, "type": "color", "color": "#2563eb"}, "style": [{"condition": [{"key": "style", "relation": "==", "value": "filled"}], "selector": "{{WRAPPER}} .blockart-button-link"}]}, "background2": {"type": "object", "default": {"background": 1}, "style": [{"condition": [{"key": "style", "relation": "==", "value": "outline"}], "selector": "{{WRAPPER}} .blockart-button-link.is-style-outline"}]}, "hoverBackground1": {"type": "object", "default": {"background": 1}, "style": [{"condition": [{"key": "style", "relation": "==", "value": "filled"}], "selector": "{{WRAPPER}} .blockart-button-link:hover, {{WRAPPER}} .blockart-button-link:focus, {{WRAPPER}} .blockart-button-link:active"}]}, "hoverBackground2": {"type": "object", "default": {"background": 1}, "style": [{"condition": [{"key": "style", "relation": "==", "value": "outline"}], "selector": "{{WRAPPER}} .blockart-button-link.is-style-outline:hover, {{WRAPPER}} .blockart-button-link.is-style-outline:focus, {{WRAPPER}} .blockart-button-link.is-style-outline:active"}]}, "border": {"type": "object", "default": {"border": 1, "radius": {"desktop": {"lock": true}}, "size": {"desktop": {"lock": true}}}, "style": [{"selector": "{{WRAPPER}}"}]}, "border1": {"type": "object", "default": {"border": 1, "radius": {"desktop": {"lock": true}}, "size": {"desktop": {"lock": true}}}, "style": [{"condition": [{"key": "style", "relation": "==", "value": "filled"}], "selector": "{{WRAPPER}} .blockart-button-link"}]}, "hoverBorder1": {"type": "object", "default": {"border": 1, "radius": {"desktop": {"lock": true}}, "size": {"desktop": {"lock": true}}}, "style": [{"condition": [{"key": "style", "relation": "==", "value": "filled"}], "selector": "{{WRAPPER}} .blockart-button-link:hover, {{WRAPPER}} .blockart-button-link:focus, {{WRAPPER}} .blockart-button-link:active"}]}, "border2": {"type": "object", "default": {"border": 1, "color": "#2563EB", "type": "solid", "radius": {"desktop": {"top": 2, "right": 2, "bottom": 2, "left": 2, "unit": "px", "lock": true}}, "size": {"desktop": {"top": 1, "right": 1, "bottom": 1, "left": 1, "unit": "px", "lock": true}}}, "style": [{"condition": [{"key": "style", "relation": "==", "value": "outline"}], "selector": "{{WRAPPER}} .blockart-button-link"}]}, "hoverBorder2": {"type": "object", "default": {"border": 1, "radius": {"desktop": {"lock": true}}, "size": {"desktop": {"lock": true}}}, "style": [{"condition": [{"key": "style", "relation": "==", "value": "outline"}], "selector": "{{WRAPPER}} .blockart-button-link:hover, {{WRAPPER}} .blockart-button-link:focus, {{WRAPPER}} .blockart-button-link:active"}]}, "positionProperty": {"type": "object", "default": {"positionProperty": 1}, "style": [{"selector": "{{WRAPPER}}"}]}, "boxShadow": {"type": "object", "default": {"boxShadow": 1}, "style": [{"selector": "{{WRAPPER}} .blockart-button-link"}]}, "boxShadowHover": {"type": "object", "default": {"boxShadow": 1}, "style": [{"selector": "{{WRAPPER}} .blockart-button-link:hover, {{WRAPPER}} .blockart-button-link:focus, {{WRAPPER}} .blockart-button-link:active"}]}, "blockMargin": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { margin: {{VALUE}}; }"}]}, "blockPadding": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { padding: {{VALUE}}; }"}]}, "blockZIndex": {"type": "number", "style": [{"selector": "{{WRAPPER}} { z-index: {{VALUE}}; }"}]}, "blockOpacity": {"type": "number", "style": [{"selector": "{{WRAPPER}} { opacity: {{VALUE}}; }"}]}, "cssID": {"type": "string"}, "animation": {"type": "string"}, "interaction": {"type": "object"}, "position": {"type": "object"}, "hideOnDesktop": {"type": "boolean", "style": [{"selector": "@media (min-width:62em) { {{WRAPPER}} { display: none !important; } }"}]}, "hideOnTablet": {"type": "boolean", "style": [{"selector": "@media (min-width:48em) and (max-width:62em) { {{WRAPPER}} { display: none !important; } }"}]}, "hideOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} { display: none !important; } }"}]}, "colReverseOnTablet": {"type": "boolean", "style": [{"selector": "@media (max-width:62em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "colReverseOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "blockCSS": {"type": "string"}, "className": {"type": "string"}}, "style": "blockart-blocks", "editorScript": "blockart-blocks", "editorStyle": "blockart-blocks-editor", "script": "blockart-frontend"}
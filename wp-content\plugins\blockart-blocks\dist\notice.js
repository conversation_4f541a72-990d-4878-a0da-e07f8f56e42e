(()=>{const{domReady:t,$$:e,toArray:o,on:a,each:s,getCookie:i,setCookie:c}=window.blockartUtils;t((function(){const t=o(e(".blockart-icon.dismiss"));t.length&&s(t,(t=>{const e=t.closest(".blockart-notice"),o=e?.dataset?.id;a("click",t,(t=>{let a=t.target;if("path"===a?.tagName&&(a=a.closest("svg")),e.style.display="none",!a?.dataset?.hide)return;const s=a?.dataset?.hide&&"-1"!==a?.dataset?.hide?parseFloat(a.dataset.hide):9999;s&&o&&c("notice_"+o,o,s)})),i("notice_"+o)||(e.style.display="block")}))}))})();
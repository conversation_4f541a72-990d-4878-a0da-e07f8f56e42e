{"$schema": "https://schemas.wp.org/trunk/block.json", "textdomain": "blockart", "title": "Column", "description": "An advanced single column within section block.", "parent": ["blockart/section"], "category": "blockart", "name": "blockart/column", "supports": {"className": false, "customClassName": false, "inserter": false, "reusable": false, "html": false}, "example": {"attributes": {}}, "attributes": {"clientId": {"type": "string", "default": ""}, "justification": {"type": "object"}, "orientation": {"type": "object"}, "enableFlex": {"type": "boolean"}, "alignment": {"type": "object"}, "rowGap": {"type": "object", "default": {"desktop": {"value": 8, "unit": "px"}}, "style": [{"selector": "{{WRAPPER}} .blockart-column-inner { row-gap: {{VALUE}}; }"}]}, "columnGap": {"type": "object", "default": {"desktop": {"value": 8, "unit": "px"}}, "style": [{"selector": "{{WRAPPER}} .blockart-column-inner { column-gap: {{VALUE}}; }"}]}, "colWidth": {"type": "object", "default": {"desktop": 50, "tablet": 100, "mobile": 100}, "style": [{"selector": "{{WRAPPER}} { width: {{VALUE}}%; }"}]}, "background": {"type": "object", "default": {"background": 1}, "style": [{"selector": "{{WRAPPER}} > .blockart-column-inner"}]}, "hoverBackground": {"type": "object", "default": {"background": 1}, "style": [{"selector": "{{WRAPPER}}:hover > .blockart-column-inner"}]}, "opacity": {"type": "string", "default": 1, "style": [{"selector": "{{WRAPPER}} > .blockart-overlay { opacity: {{VALUE}}; }"}]}, "overlay": {"type": "boolean", "default": false}, "overlayBackground": {"type": "object", "default": {"background": 1, "color": "rgba(37, 99, 235,0.3)"}, "style": [{"condition": [{"key": "overlay", "relation": "==", "value": true}], "selector": "{{WRAPPER}} > .blockart-overlay"}]}, "blurEffect": {"type": "object", "style": [{"selector": "{{WRAPPER}} > .blockart-column-inner { backdrop-filter: blur({{VALUE}}px); }"}]}, "border": {"type": "object", "default": {"border": 1, "radius": {"desktop": {"lock": true}}, "size": {"desktop": {"lock": true}}}, "style": [{"selector": "{{WRAPPER}} > .blockart-column-inner"}]}, "borderHover": {"type": "object", "default": {"border": 1, "radius": {"desktop": {"lock": true}}, "size": {"desktop": {"lock": true}}}, "style": [{"selector": "{{WRAPPER}}:hover > .blockart-column-inner"}]}, "boxShadow": {"type": "object", "default": {"boxShadow": 1}, "style": [{"selector": "{{WRAPPER}} > .blockart-column-inner"}]}, "boxShadowHover": {"type": "object", "default": {"boxShadow": 1}, "style": [{"selector": "{{WRAPPER}}:hover > .blockart-column-inner"}]}, "blockMargin": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { margin: {{VALUE}}; }"}]}, "blockPadding": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} > .blockart-column-inner { padding: {{VALUE}}; }"}]}, "blockZIndex": {"type": "number", "style": [{"selector": "{{WRAPPER}} { z-index: {{VALUE}}; }"}]}, "cssID": {"type": "string"}, "animation": {"type": "string"}, "interaction": {"type": "object"}, "position": {"type": "object"}, "hideOnDesktop": {"type": "boolean", "style": [{"selector": "@media (min-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnTablet": {"type": "boolean", "style": [{"selector": "@media (min-width:48em) and (max-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} { display: none; } }"}]}, "colReverseOnTablet": {"type": "boolean", "style": [{"selector": "@media (max-width:62em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "colReverseOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "blockCSS": {"type": "string"}, "className": {"type": "string"}}, "style": "blockart-blocks", "editorScript": "blockart-blocks", "editorStyle": "blockart-blocks-editor"}
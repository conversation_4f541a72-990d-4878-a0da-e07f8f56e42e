/*! For license information please see 353.js.LICENSE.txt */
"use strict";(self.webpackChunkblockart_blocks=self.webpackChunkblockart_blocks||[]).push([[353],{353:(t,n,e)=>{function i(t,n){for(var e=0;e<n.length;e++){var i=n[e];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}e.r(n),e.d(n,{CLASSES:()=>te,CLASS_ACTIVE:()=>Yn,CLASS_ARROW:()=>kn,CLASS_ARROWS:()=>On,CLASS_ARROW_NEXT:()=>Vn,CLASS_ARROW_PREV:()=>Rn,CLASS_CLONE:()=>Pn,CLASS_CONTAINER:()=>Dn,CLASS_FOCUS_IN:()=>Jn,CLASS_INITIALIZED:()=>Bn,CLASS_LIST:()=>Nn,CLASS_LOADING:()=>Zn,CLASS_NEXT:()=>Kn,CLASS_OVERFLOW:()=>Qn,CLASS_PAGINATION:()=>In,CLASS_PAGINATION_PAGE:()=>Mn,CLASS_PREV:()=>Xn,CLASS_PROGRESS:()=>Gn,CLASS_PROGRESS_BAR:()=>zn,CLASS_ROOT:()=>wn,CLASS_SLIDE:()=>xn,CLASS_SPINNER:()=>Un,CLASS_SR:()=>jn,CLASS_TOGGLE:()=>Wn,CLASS_TOGGLE_PAUSE:()=>Hn,CLASS_TOGGLE_PLAY:()=>Fn,CLASS_TRACK:()=>Tn,CLASS_VISIBLE:()=>qn,DEFAULTS:()=>Se,EVENT_ACTIVE:()=>bt,EVENT_ARROWS_MOUNTED:()=>It,EVENT_ARROWS_UPDATED:()=>Mt,EVENT_AUTOPLAY_PAUSE:()=>Ut,EVENT_AUTOPLAY_PLAY:()=>Ft,EVENT_AUTOPLAY_PLAYING:()=>Ht,EVENT_CLICK:()=>yt,EVENT_DESTROY:()=>Vt,EVENT_DRAG:()=>xt,EVENT_DRAGGED:()=>Dt,EVENT_DRAGGING:()=>Pt,EVENT_END_INDEX_CHANGED:()=>Xt,EVENT_HIDDEN:()=>Ct,EVENT_INACTIVE:()=>_t,EVENT_LAZYLOAD_LOADED:()=>jt,EVENT_MOUNTED:()=>gt,EVENT_MOVE:()=>Et,EVENT_MOVED:()=>St,EVENT_NAVIGATION_MOUNTED:()=>Wt,EVENT_OVERFLOW:()=>Rt,EVENT_PAGINATION_MOUNTED:()=>Gt,EVENT_PAGINATION_UPDATED:()=>zt,EVENT_READY:()=>mt,EVENT_REFRESH:()=>Lt,EVENT_RESIZE:()=>Tt,EVENT_RESIZED:()=>Nt,EVENT_SCROLL:()=>Ot,EVENT_SCROLLED:()=>kt,EVENT_SHIFTED:()=>Yt,EVENT_SLIDE_KEYDOWN:()=>Bt,EVENT_UPDATED:()=>wt,EVENT_VISIBLE:()=>At,EventBinder:()=>vt,EventInterface:()=>Kt,FADE:()=>se,LOOP:()=>oe,LTR:()=>rn,RTL:()=>on,RequestInterval:()=>qt,SLIDE:()=>re,STATUS_CLASSES:()=>$n,Splide:()=>_e,SplideRenderer:()=>Le,State:()=>Zt,TTB:()=>sn,Throttle:()=>Jt,default:()=>_e});var r="(prefers-reduced-motion: reduce)";function o(t){t.length=0}function s(t,n,e){return Array.prototype.slice.call(t,n,e)}function u(t){return t.bind.apply(t,[null].concat(s(arguments,1)))}var a=setTimeout,c=function(){};function l(t){return requestAnimationFrame(t)}function f(t,n){return typeof n===t}function d(t){return!m(t)&&f("object",t)}var h=Array.isArray,p=u(f,"function"),v=u(f,"string"),g=u(f,"undefined");function m(t){return null===t}function E(t){try{return t instanceof(t.ownerDocument.defaultView||window).HTMLElement}catch(t){return!1}}function S(t){return h(t)?t:[t]}function y(t,n){S(t).forEach(n)}function b(t,n){return t.indexOf(n)>-1}function _(t,n){return t.push.apply(t,S(n)),t}function A(t,n,e){t&&y(n,(function(n){n&&t.classList[e?"add":"remove"](n)}))}function C(t,n){A(t,v(n)?n.split(" "):n,!0)}function L(t,n){y(n,t.appendChild.bind(t))}function w(t,n){y(t,(function(t){var e=(n||t).parentNode;e&&e.insertBefore(t,n)}))}function T(t,n){return E(t)&&(t.msMatchesSelector||t.matches).call(t,n)}function N(t,n){var e=t?s(t.children):[];return n?e.filter((function(t){return T(t,n)})):e}function x(t,n){return n?N(t,n)[0]:t.firstElementChild}var P=Object.keys;function D(t,n,e){return t&&(e?P(t).reverse():P(t)).forEach((function(e){"__proto__"!==e&&n(t[e],e)})),t}function O(t){return s(arguments,1).forEach((function(n){D(n,(function(e,i){t[i]=n[i]}))})),t}function k(t){return s(arguments,1).forEach((function(n){D(n,(function(n,e){h(n)?t[e]=n.slice():d(n)?t[e]=k({},d(t[e])?t[e]:{},n):t[e]=n}))})),t}function R(t,n){y(n||P(t),(function(n){delete t[n]}))}function V(t,n){y(t,(function(t){y(n,(function(n){t&&t.removeAttribute(n)}))}))}function I(t,n,e){d(n)?D(n,(function(n,e){I(t,e,n)})):y(t,(function(t){m(e)||""===e?V(t,n):t.setAttribute(n,String(e))}))}function M(t,n,e){var i=document.createElement(t);return n&&(v(n)?C(i,n):I(i,n)),e&&L(e,i),i}function G(t,n,e){if(g(e))return getComputedStyle(t)[n];m(e)||(t.style[n]=""+e)}function z(t,n){G(t,"display",n)}function W(t){t.setActive&&t.setActive()||t.focus({preventScroll:!0})}function F(t,n){return t.getAttribute(n)}function H(t,n){return t&&t.classList.contains(n)}function U(t){return t.getBoundingClientRect()}function j(t){y(t,(function(t){t&&t.parentNode&&t.parentNode.removeChild(t)}))}function B(t){return x((new DOMParser).parseFromString(t,"text/html").body)}function Y(t,n){t.preventDefault(),n&&(t.stopPropagation(),t.stopImmediatePropagation())}function X(t,n){return t&&t.querySelector(n)}function K(t,n){return n?s(t.querySelectorAll(n)):[]}function q(t,n){A(t,n,!1)}function Z(t){return t.timeStamp}function J(t){return v(t)?t:t?t+"px":""}var Q="splide",$="data-"+Q;function tt(t,n){if(!t)throw new Error("["+Q+"] "+(n||""))}var nt=Math.min,et=Math.max,it=Math.floor,rt=Math.ceil,ot=Math.abs;function st(t,n,e){return ot(t-n)<e}function ut(t,n,e,i){var r=nt(n,e),o=et(n,e);return i?r<t&&t<o:r<=t&&t<=o}function at(t,n,e){var i=nt(n,e),r=et(n,e);return nt(et(i,t),r)}function ct(t){return+(t>0)-+(t<0)}function lt(t){return t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}function ft(t,n){return y(n,(function(n){t=t.replace("%s",""+n)})),t}function dt(t){return t<10?"0"+t:""+t}var ht={};function pt(t){return""+t+dt(ht[t]=(ht[t]||0)+1)}function vt(){var t=[];function n(t,n,e){y(t,(function(t){t&&y(n,(function(n){n.split(" ").forEach((function(n){var i=n.split(".");e(t,i[0],i[1])}))}))}))}return{bind:function(e,i,r,o){n(e,i,(function(n,e,i){var s="addEventListener"in n,u=s?n.removeEventListener.bind(n,e,r,o):n.removeListener.bind(n,r);s?n.addEventListener(e,r,o):n.addListener(r),t.push([n,e,i,r,u])}))},unbind:function(e,i,r){n(e,i,(function(n,e,i){t=t.filter((function(t){return!!(t[0]!==n||t[1]!==e||t[2]!==i||r&&t[3]!==r)||(t[4](),!1)}))}))},dispatch:function(t,n,e){var i,r=!0;return"function"==typeof CustomEvent?i=new CustomEvent(n,{bubbles:r,detail:e}):(i=document.createEvent("CustomEvent")).initCustomEvent(n,r,!1,e),t.dispatchEvent(i),i},destroy:function(){t.forEach((function(t){t[4]()})),o(t)}}}var gt="mounted",mt="ready",Et="move",St="moved",yt="click",bt="active",_t="inactive",At="visible",Ct="hidden",Lt="refresh",wt="updated",Tt="resize",Nt="resized",xt="drag",Pt="dragging",Dt="dragged",Ot="scroll",kt="scrolled",Rt="overflow",Vt="destroy",It="arrows:mounted",Mt="arrows:updated",Gt="pagination:mounted",zt="pagination:updated",Wt="navigation:mounted",Ft="autoplay:play",Ht="autoplay:playing",Ut="autoplay:pause",jt="lazyload:loaded",Bt="sk",Yt="sh",Xt="ei";function Kt(t){var n=t?t.event.bus:document.createDocumentFragment(),e=vt();return t&&t.event.on(Vt,e.destroy),O(e,{bus:n,on:function(t,i){e.bind(n,S(t).join(" "),(function(t){i.apply(i,h(t.detail)?t.detail:[])}))},off:u(e.unbind,n),emit:function(t){e.dispatch(n,t,s(arguments,1))}})}function qt(t,n,e,i){var r,o,s=Date.now,u=0,a=!0,c=0;function f(){if(!a){if(u=t?nt((s()-r)/t,1):1,e&&e(u),u>=1&&(n(),r=s(),i&&++c>=i))return d();o=l(f)}}function d(){a=!0}function h(){o&&cancelAnimationFrame(o),u=0,o=0,a=!0}return{start:function(n){n||h(),r=s()-(n?u*t:0),a=!1,o=l(f)},rewind:function(){r=s(),u=0,e&&e(u)},pause:d,cancel:h,set:function(n){t=n},isPaused:function(){return a}}}function Zt(t){var n=t;return{set:function(t){n=t},is:function(t){return b(S(t),n)}}}function Jt(t,n){var e=qt(n||0,t,null,1);return function(){e.isPaused()&&e.start()}}var Qt="Arrow",$t=Qt+"Left",tn=Qt+"Right",nn=Qt+"Up",en=Qt+"Down",rn="ltr",on="rtl",sn="ttb",un={width:["height"],left:["top","right"],right:["bottom","left"],x:["y"],X:["Y"],Y:["X"],ArrowLeft:[nn,tn],ArrowRight:[en,$t]};function an(t,n,e){return{resolve:function(t,n,i){var r=(i=i||e.direction)!==on||n?i===sn?0:-1:1;return un[t]&&un[t][r]||t.replace(/width|left|right/i,(function(t,n){var e=un[t.toLowerCase()][r]||t;return n>0?e.charAt(0).toUpperCase()+e.slice(1):e}))},orient:function(t){return t*(e.direction===on?1:-1)}}}var cn="role",ln="tabindex",fn="aria-",dn=fn+"controls",hn=fn+"current",pn=fn+"selected",vn=fn+"label",gn=fn+"labelledby",mn=fn+"hidden",En=fn+"orientation",Sn=fn+"roledescription",yn=fn+"live",bn=fn+"busy",_n=fn+"atomic",An=[cn,ln,"disabled",dn,hn,vn,gn,mn,En,Sn],Cn=Q+"__",Ln="is-",wn=Q,Tn=Cn+"track",Nn=Cn+"list",xn=Cn+"slide",Pn=xn+"--clone",Dn=xn+"__container",On=Cn+"arrows",kn=Cn+"arrow",Rn=kn+"--prev",Vn=kn+"--next",In=Cn+"pagination",Mn=In+"__page",Gn=Cn+"progress",zn=Gn+"__bar",Wn=Cn+"toggle",Fn=Wn+"__play",Hn=Wn+"__pause",Un=Cn+"spinner",jn=Cn+"sr",Bn=Ln+"initialized",Yn=Ln+"active",Xn=Ln+"prev",Kn=Ln+"next",qn=Ln+"visible",Zn=Ln+"loading",Jn=Ln+"focus-in",Qn=Ln+"overflow",$n=[Yn,qn,Xn,Kn,Zn,Jn,Qn],te={slide:xn,clone:Pn,arrows:On,arrow:kn,prev:Rn,next:Vn,pagination:In,page:Mn,spinner:Un},ne="touchstart mousedown",ee="touchmove mousemove",ie="touchend touchcancel mouseup click",re="slide",oe="loop",se="fade";var ue="http://www.w3.org/2000/svg",ae="m15.5 0.932-4.3 4.38 14.5 14.6-14.5 14.5 4.3 4.4 14.6-14.6 4.4-4.3-4.4-4.4-14.6-14.6z",ce=$+"-interval",le={passive:!1,capture:!0},fe={Spacebar:" ",Right:tn,Left:$t,Up:nn,Down:en};function de(t){return t=v(t)?t:t.key,fe[t]||t}var he="keydown",pe=$+"-lazy",ve=pe+"-srcset",ge="["+pe+"], ["+ve+"]",me=[" ","Enter"],Ee=Object.freeze({__proto__:null,Media:function(t,n,e){var i=t.state,o=e.breakpoints||{},s=e.reducedMotion||{},u=vt(),a=[];function c(t){t&&u.destroy()}function l(t,n){var e=matchMedia(n);u.bind(e,"change",f),a.push([t,e])}function f(){var n=i.is(7),r=e.direction,o=a.reduce((function(t,n){return k(t,n[1].matches?n[0]:{})}),{});R(e),d(o),e.destroy?t.destroy("completely"===e.destroy):n?(c(!0),t.mount()):r!==e.direction&&t.refresh()}function d(n,r,o){k(e,n),r&&k(Object.getPrototypeOf(e),n),!o&&i.is(1)||t.emit(wt,e)}return{setup:function(){var t="min"===e.mediaQuery;P(o).sort((function(n,e){return t?+n-+e:+e-+n})).forEach((function(n){l(o[n],"("+(t?"min":"max")+"-width:"+n+"px)")})),l(s,r),f()},destroy:c,reduce:function(t){matchMedia(r).matches&&(t?k(e,s):R(e,P(s)))},set:d}},Direction:an,Elements:function(t,n,e){var i,r,s,u=Kt(t),a=u.on,c=u.bind,l=t.root,f=e.i18n,d={},h=[],v=[],g=[];function m(){var t,n;i=y("."+Tn),r=x(i,"."+Nn),tt(i&&r,"A track/list element is missing."),_(h,N(r,"."+xn+":not(."+Pn+")")),D({arrows:On,pagination:In,prev:Rn,next:Vn,bar:zn,toggle:Wn},(function(t,n){d[n]=y("."+t)})),O(d,{root:l,track:i,list:r,slides:h}),t=l.id||pt(Q),n=e.role,l.id=t,i.id=i.id||t+"-track",r.id=r.id||t+"-list",!F(l,cn)&&"SECTION"!==l.tagName&&n&&I(l,cn,n),I(l,Sn,f.carousel),I(r,cn,"presentation"),S()}function E(t){var n=An.concat("style");o(h),q(l,v),q(i,g),V([i,r],n),V(l,t?n:["style",Sn])}function S(){q(l,v),q(i,g),v=b(wn),g=b(Tn),C(l,v),C(i,g),I(l,vn,e.label),I(l,gn,e.labelledby)}function y(t){var n=X(l,t);return n&&function(t,n){if(p(t.closest))return t.closest(n);for(var e=t;e&&1===e.nodeType&&!T(e,n);)e=e.parentElement;return e}(n,"."+wn)===l?n:void 0}function b(t){return[t+"--"+e.type,t+"--"+e.direction,e.drag&&t+"--draggable",e.isNavigation&&t+"--nav",t===wn&&Yn]}return O(d,{setup:m,mount:function(){a(Lt,E),a(Lt,m),a(wt,S),c(document,ne+" keydown",(function(t){s="keydown"===t.type}),{capture:!0}),c(l,"focusin",(function(){A(l,Jn,!!s)}))},destroy:E})},Slides:function(t,n,e){var i=Kt(t),r=i.on,s=i.emit,a=i.bind,c=n.Elements,l=c.slides,f=c.list,d=[];function h(){l.forEach((function(t,n){m(t,n,-1)}))}function g(){N((function(t){t.destroy()})),o(d)}function m(n,e,i){var r=function(t,n,e,i){var r,o=Kt(t),s=o.on,a=o.emit,c=o.bind,l=t.Components,f=t.root,d=t.options,h=d.isNavigation,p=d.updateOnMove,v=d.i18n,g=d.pagination,m=d.slideFocus,E=l.Direction.resolve,S=F(i,"style"),y=F(i,vn),b=e>-1,_=x(i,"."+Dn);function C(){var r=t.splides.map((function(t){var e=t.splide.Components.Slides.getAt(n);return e?e.slide.id:""})).join(" ");I(i,vn,ft(v.slideX,(b?e:n)+1)),I(i,dn,r),I(i,cn,m?"button":""),m&&V(i,Sn)}function L(){r||w()}function w(){if(!r){var e=t.index;(o=T())!==H(i,Yn)&&(A(i,Yn,o),I(i,hn,h&&o||""),a(o?bt:_t,N)),function(){var n=function(){if(t.is(se))return T();var n=U(l.Elements.track),e=U(i),r=E("left",!0),o=E("right",!0);return it(n[r])<=rt(e[r])&&it(e[o])<=rt(n[o])}(),e=!n&&(!T()||b);if(t.state.is([4,5])||I(i,mn,e||""),I(K(i,d.focusableNodes||""),ln,e?-1:""),m&&I(i,ln,e?-1:0),n!==H(i,qn)&&(A(i,qn,n),a(n?At:Ct,N)),!n&&document.activeElement===i){var r=l.Slides.getAt(t.index);r&&W(r.slide)}}(),A(i,Xn,n===e-1),A(i,Kn,n===e+1)}var o}function T(){var i=t.index;return i===n||d.cloneStatus&&i===e}var N={index:n,slideIndex:e,slide:i,container:_,isClone:b,mount:function(){b||(i.id=f.id+"-slide"+dt(n+1),I(i,cn,g?"tabpanel":"group"),I(i,Sn,v.slide),I(i,vn,y||ft(v.slideLabel,[n+1,t.length]))),c(i,"click",u(a,yt,N)),c(i,"keydown",u(a,Bt,N)),s([St,Yt,kt],w),s(Wt,C),p&&s(Et,L)},destroy:function(){r=!0,o.destroy(),q(i,$n),V(i,An),I(i,"style",S),I(i,vn,y||"")},update:w,style:function(t,n,e){G(e&&_||i,t,n)},isWithin:function(e,i){var r=ot(e-n);return b||!d.rewind&&!t.is(oe)||(r=nt(r,t.length-r)),r<=i}};return N}(t,e,i,n);r.mount(),d.push(r),d.sort((function(t,n){return t.index-n.index}))}function _(t){return t?P((function(t){return!t.isClone})):d}function N(t,n){_(n).forEach(t)}function P(t){return d.filter(p(t)?t:function(n){return v(t)?T(n.slide,t):b(S(t),n.index)})}return{mount:function(){h(),r(Lt,g),r(Lt,h)},destroy:g,update:function(){N((function(t){t.update()}))},register:m,get:_,getIn:function(t){var i=n.Controller,r=i.toIndex(t),o=i.hasFocus()?1:e.perPage;return P((function(t){return ut(t.index,r,r+o-1)}))},getAt:function(t){return P(t)[0]},add:function(t,n){y(t,(function(t){if(v(t)&&(t=B(t)),E(t)){var i=l[n];i?w(t,i):L(f,t),C(t,e.classes.slide),r=t,o=u(s,Tt),c=K(r,"img"),(d=c.length)?c.forEach((function(t){a(t,"load error",(function(){--d||o()}))})):o()}var r,o,c,d})),s(Lt)},remove:function(t){j(P(t).map((function(t){return t.slide}))),s(Lt)},forEach:N,filter:P,style:function(t,n,e){N((function(i){i.style(t,n,e)}))},getLength:function(t){return t?l.length:d.length},isEnough:function(){return d.length>e.perPage}}},Layout:function(t,n,e){var i,r,o,s=Kt(t),a=s.on,c=s.bind,l=s.emit,f=n.Slides,h=n.Direction.resolve,p=n.Elements,v=p.root,g=p.track,m=p.list,E=f.getAt,S=f.style;function y(){i=e.direction===sn,G(v,"maxWidth",J(e.width)),G(g,h("paddingLeft"),_(!1)),G(g,h("paddingRight"),_(!0)),b(!0)}function b(t){var n,s=U(v);(t||r.width!==s.width||r.height!==s.height)&&(G(g,"height",(n="",i&&(tt(n=C(),"height or heightRatio is missing."),n="calc("+n+" - "+_(!1)+" - "+_(!0)+")"),n)),S(h("marginRight"),J(e.gap)),S("width",e.autoWidth?null:J(e.fixedWidth)||(i?"":L())),S("height",J(e.fixedHeight)||(i?e.autoHeight?null:L():C()),!0),r=s,l(Nt),o!==(o=D())&&(A(v,Qn,o),l(Rt,o)))}function _(t){var n=e.padding,i=h(t?"right":"left");return n&&J(n[i]||(d(n)?0:n))||"0px"}function C(){return J(e.height||U(m).width*e.heightRatio)}function L(){var t=J(e.gap);return"calc((100%"+(t&&" + "+t)+")/"+(e.perPage||1)+(t&&" - "+t)+")"}function w(){return U(m)[h("width")]}function T(t,n){var e=E(t||0);return e?U(e.slide)[h("width")]+(n?0:P()):0}function N(t,n){var e=E(t);if(e){var i=U(e.slide)[h("right")],r=U(m)[h("left")];return ot(i-r)+(n?0:P())}return 0}function x(n){return N(t.length-1)-N(0)+T(0,n)}function P(){var t=E(0);return t&&parseFloat(G(t.slide,h("marginRight")))||0}function D(){return t.is(se)||x(!0)>w()}return{mount:function(){y(),c(window,"resize load",Jt(u(l,Tt))),a([wt,Lt],y),a(Tt,b)},resize:b,listSize:w,slideSize:T,sliderSize:x,totalSize:N,getPadding:function(t){return parseFloat(G(g,h("padding"+(t?"Right":"Left"))))||0},isOverflow:D}},Clones:function(t,n,e){var i,r=Kt(t),s=r.on,u=n.Elements,a=n.Slides,c=n.Direction.resolve,l=[];function f(){s(Lt,d),s([wt,Tt],p),(i=v())&&(function(n){var i=a.get().slice(),r=i.length;if(r){for(;i.length<n;)_(i,i);_(i.slice(-n),i.slice(0,n)).forEach((function(o,s){var c=s<n,f=function(n,i){var r=n.cloneNode(!0);return C(r,e.classes.clone),r.id=t.root.id+"-clone"+dt(i+1),r}(o.slide,s);c?w(f,i[0].slide):L(u.list,f),_(l,f),a.register(f,s-n+(c?0:r),o.index)}))}}(i),n.Layout.resize(!0))}function d(){h(),f()}function h(){j(l),o(l),r.destroy()}function p(){var t=v();i!==t&&(i<t||!t)&&r.emit(Lt)}function v(){var i=e.clones;if(t.is(oe)){if(g(i)){var r=e[c("fixedWidth")]&&n.Layout.slideSize(0);i=r&&rt(U(u.track)[c("width")]/r)||e[c("autoWidth")]&&t.length||2*e.perPage}}else i=0;return i}return{mount:f,destroy:h}},Move:function(t,n,e){var i,r=Kt(t),o=r.on,s=r.emit,u=t.state.set,a=n.Layout,c=a.slideSize,l=a.getPadding,f=a.totalSize,d=a.listSize,h=a.sliderSize,p=n.Direction,v=p.resolve,m=p.orient,E=n.Elements,S=E.list,y=E.track;function b(){n.Controller.isBusy()||(n.Scroll.cancel(),_(t.index),n.Slides.update())}function _(t){A(T(t,!0))}function A(e,i){if(!t.is(se)){var r=i?e:function(e){if(t.is(oe)){var i=w(e),r=i>n.Controller.getEnd();(i<0||r)&&(e=C(e,r))}return e}(e);G(S,"transform","translate"+v("X")+"("+r+"px)"),e!==r&&s(Yt)}}function C(t,n){var e=t-x(n),i=h();return t-m(i*(rt(ot(e)/i)||1))*(n?1:-1)}function L(){A(N(),!0),i.cancel()}function w(t){for(var e=n.Slides.get(),i=0,r=1/0,o=0;o<e.length;o++){var s=e[o].index,u=ot(T(s,!0)-t);if(!(u<=r))break;r=u,i=s}return i}function T(n,i){var r=m(f(n-1)-function(t){var n=e.focus;return"center"===n?(d()-c(t,!0))/2:+n*c(t)||0}(n));return i?function(n){return e.trimSpace&&t.is(re)&&(n=at(n,0,m(h(!0)-d()))),n}(r):r}function N(){var t=v("left");return U(S)[t]-U(y)[t]+m(l(!1))}function x(t){return T(t?n.Controller.getEnd():0,!!e.trimSpace)}return{mount:function(){i=n.Transition,o([gt,Nt,wt,Lt],b)},move:function(t,n,e,r){var o,a;t!==n&&(o=t>e,a=m(C(N(),o)),o?a>=0:a<=S[v("scrollWidth")]-U(y)[v("width")])&&(L(),A(C(N(),t>e),!0)),u(4),s(Et,n,e,t),i.start(n,(function(){u(3),s(St,n,e,t),r&&r()}))},jump:_,translate:A,shift:C,cancel:L,toIndex:w,toPosition:T,getPosition:N,getLimit:x,exceededLimit:function(t,n){n=g(n)?N():n;var e=!0!==t&&m(n)<m(x(!1)),i=!1!==t&&m(n)>m(x(!0));return e||i},reposition:b}},Controller:function(t,n,e){var i,r,o,s,a=Kt(t),c=a.on,l=a.emit,f=n.Move,d=f.getPosition,h=f.getLimit,p=f.toPosition,m=n.Slides,E=m.isEnough,S=m.getLength,y=e.omitEnd,b=t.is(oe),_=t.is(re),A=u(x,!1),C=u(x,!0),L=e.start||0,w=L;function T(){r=S(!0),o=e.perMove,s=e.perPage,i=O();var t=at(L,0,y?i:r-1);t!==L&&(L=t,f.reposition())}function N(){i!==O()&&l(Xt)}function x(t,n){var e=o||(I()?1:s),r=P(L+e*(t?-1:1),L,!(o||I()));return-1===r&&_&&!st(d(),h(!t),1)?t?0:i:n?r:D(r)}function P(n,u,a){if(E()||I()){var c=function(n){if(_&&"move"===e.trimSpace&&n!==L)for(var i=d();i===p(n,!0)&&ut(n,0,t.length-1,!e.rewind);)n<L?--n:++n;return n}(n);c!==n&&(u=n,n=c,a=!1),n<0||n>i?n=o||!ut(0,n,u,!0)&&!ut(i,u,n,!0)?b?a?n<0?-(r%s||s):r:n:e.rewind?n<0?i:0:-1:k(R(n)):a&&n!==u&&(n=k(R(u)+(n<u?-1:1)))}else n=-1;return n}function D(t){return b?(t+r)%r||0:t}function O(){for(var t=r-(I()||b&&o?1:s);y&&t-- >0;)if(p(r-1,!0)!==p(t,!0)){t++;break}return at(t,0,r-1)}function k(t){return at(I()?t:s*t,0,i)}function R(t){return I()?nt(t,i):it((t>=i?r-1:t)/s)}function V(t){t!==L&&(w=L,L=t)}function I(){return!g(e.focus)||e.isNavigation}function M(){return t.state.is([4,5])&&!!e.waitForTransition}return{mount:function(){T(),c([wt,Lt,Xt],T),c(Nt,N)},go:function(t,n,e){if(!M()){var r=function(t){var n=L;if(v(t)){var e=t.match(/([+\-<>])(\d+)?/)||[],r=e[1],o=e[2];"+"===r||"-"===r?n=P(L+ +(""+r+(+o||1)),L):">"===r?n=o?k(+o):A(!0):"<"===r&&(n=C(!0))}else n=b?t:at(t,0,i);return n}(t),o=D(r);o>-1&&(n||o!==L)&&(V(o),f.move(r,o,w,e))}},scroll:function(t,e,r,o){n.Scroll.scroll(t,e,r,(function(){var t=D(f.toIndex(d()));V(y?nt(t,i):t),o&&o()}))},getNext:A,getPrev:C,getAdjacent:x,getEnd:O,setIndex:V,getIndex:function(t){return t?w:L},toIndex:k,toPage:R,toDest:function(t){var n=f.toIndex(t);return _?at(n,0,i):n},hasFocus:I,isBusy:M}},Arrows:function(t,n,e){var i,r,o=Kt(t),s=o.on,a=o.bind,c=o.emit,l=e.classes,f=e.i18n,d=n.Elements,h=n.Controller,p=d.arrows,v=d.track,g=p,m=d.prev,E=d.next,S={};function y(){var t;!(t=e.arrows)||m&&E||(g=p||M("div",l.arrows),m=T(!0),E=T(!1),i=!0,L(g,[m,E]),!p&&w(g,v)),m&&E&&(O(S,{prev:m,next:E}),z(g,t?"":"none"),C(g,r=On+"--"+e.direction),t&&(s([gt,St,Lt,kt,Xt],N),a(E,"click",u(A,">")),a(m,"click",u(A,"<")),N(),I([m,E],dn,v.id),c(It,m,E))),s(wt,b)}function b(){_(),y()}function _(){o.destroy(),q(g,r),i?(j(p?[m,E]:g),m=E=null):V([m,E],An)}function A(t){h.go(t,!0)}function T(t){return B('<button class="'+l.arrow+" "+(t?l.prev:l.next)+'" type="button"><svg xmlns="'+ue+'" viewBox="0 0 40 40" width="40" height="40" focusable="false"><path d="'+(e.arrowPath||ae)+'" />')}function N(){if(m&&E){var n=t.index,e=h.getPrev(),i=h.getNext(),r=e>-1&&n<e?f.last:f.prev,o=i>-1&&n>i?f.first:f.next;m.disabled=e<0,E.disabled=i<0,I(m,vn,r),I(E,vn,o),c(Mt,m,E,e,i)}}return{arrows:S,mount:y,destroy:_,update:N}},Autoplay:function(t,n,e){var i,r,o=Kt(t),s=o.on,u=o.bind,a=o.emit,c=qt(e.interval,t.go.bind(t,">"),(function(t){var n=f.bar;n&&G(n,"width",100*t+"%"),a(Ht,t)})),l=c.isPaused,f=n.Elements,d=n.Elements,h=d.root,p=d.toggle,v=e.autoplay,g="pause"===v;function m(){l()&&n.Slides.isEnough()&&(c.start(!e.resetProgress),r=i=g=!1,y(),a(Ft))}function E(t){void 0===t&&(t=!0),g=!!t,y(),l()||(c.pause(),a(Ut))}function S(){g||(i||r?E(!1):m())}function y(){p&&(A(p,Yn,!g),I(p,vn,e.i18n[g?"play":"pause"]))}function b(t){var i=n.Slides.getAt(t);c.set(i&&+F(i.slide,ce)||e.interval)}return{mount:function(){v&&(e.pauseOnHover&&u(h,"mouseenter mouseleave",(function(t){i="mouseenter"===t.type,S()})),e.pauseOnFocus&&u(h,"focusin focusout",(function(t){r="focusin"===t.type,S()})),p&&u(p,"click",(function(){g?m():E(!0)})),s([Et,Ot,Lt],c.rewind),s(Et,b),p&&I(p,dn,f.track.id),g||m(),y())},destroy:c.cancel,play:m,pause:E,isPaused:l}},Cover:function(t,n,e){var i=Kt(t).on;function r(t){n.Slides.forEach((function(n){var e=x(n.container||n.slide,"img");e&&e.src&&o(t,e,n)}))}function o(t,n,e){e.style("background",t?'center/cover no-repeat url("'+n.src+'")':"",!0),z(n,t?"none":"")}return{mount:function(){e.cover&&(i(jt,u(o,!0)),i([gt,wt,Lt],u(r,!0)))},destroy:u(r,!1)}},Scroll:function(t,n,e){var i,r,o=Kt(t),s=o.on,a=o.emit,c=t.state.set,l=n.Move,f=l.getPosition,d=l.getLimit,h=l.exceededLimit,p=l.translate,v=t.is(re),g=1;function m(t,e,o,s,d){var p=f();if(y(),o&&(!v||!h())){var m=n.Layout.sliderSize(),b=ct(t)*m*it(ot(t)/m)||0;t=l.toPosition(n.Controller.toDest(t%m))+b}var _=st(p,t,1);g=1,e=_?0:e||et(ot(t-p)/1.5,800),r=s,i=qt(e,E,u(S,p,t,d),1),c(5),a(Ot),i.start()}function E(){c(3),r&&r(),a(kt)}function S(t,n,i,o){var s,u,a=f(),c=(t+(n-t)*(s=o,(u=e.easingFunc)?u(s):1-Math.pow(1-s,4))-a)*g;p(a+c),v&&!i&&h()&&(g*=.6,ot(c)<10&&m(d(h(!0)),600,!1,r,!0))}function y(){i&&i.cancel()}function b(){i&&!i.isPaused()&&(y(),E())}return{mount:function(){s(Et,y),s([wt,Lt],b)},destroy:y,scroll:m,cancel:b}},Drag:function(t,n,e){var i,r,o,s,u,a,l,f,h=Kt(t),p=h.on,v=h.emit,g=h.bind,m=h.unbind,E=t.state,S=n.Move,y=n.Scroll,b=n.Controller,_=n.Elements.track,A=n.Media.reduce,C=n.Direction,L=C.resolve,w=C.orient,N=S.getPosition,x=S.exceededLimit,P=!1;function D(){var t=e.drag;H(!t),s="free"===t}function O(t){if(a=!1,!l){var n=F(t);i=t.target,r=e.noDrag,T(i,"."+Mn+", ."+kn)||r&&T(i,r)||!n&&t.button||(b.isBusy()?Y(t,!0):(f=n?_:window,u=E.is([4,5]),o=null,g(f,ee,k,le),g(f,ie,R,le),S.cancel(),y.cancel(),I(t)))}var i,r}function k(n){if(E.is(6)||(E.set(6),v(xt)),n.cancelable)if(u){S.translate(i+M(n)/(P&&t.is(re)?5:1));var r=G(n)>200,o=P!==(P=x());(r||o)&&I(n),a=!0,v(Pt),Y(n)}else(function(t){return ot(M(t))>ot(M(t,!0))})(n)&&(u=function(t){var n=e.dragMinThreshold,i=d(n),r=i&&n.mouse||0,o=(i?n.touch:+n)||10;return ot(M(t))>(F(t)?o:r)}(n),Y(n))}function R(i){E.is(6)&&(E.set(3),v(Dt)),u&&(function(i){var r=function(n){if(t.is(oe)||!P){var e=G(n);if(e&&e<200)return M(n)/e}return 0}(i),o=function(t){return N()+ct(t)*nt(ot(t)*(e.flickPower||600),s?1/0:n.Layout.listSize()*(e.flickMaxPages||1))}(r),u=e.rewind&&e.rewindByDrag;A(!1),s?b.scroll(o,0,e.snap):t.is(se)?b.go(w(ct(r))<0?u?"<":"-":u?">":"+"):t.is(re)&&P&&u?b.go(x(!0)?">":"<"):b.go(b.toDest(o),!0),A(!0)}(i),Y(i)),m(f,ee,k),m(f,ie,R),u=!1}function V(t){!l&&a&&Y(t,!0)}function I(t){o=r,r=t,i=N()}function M(t,n){return W(t,n)-W(z(t),n)}function G(t){return Z(t)-Z(z(t))}function z(t){return r===t&&o||r}function W(t,n){return(F(t)?t.changedTouches[0]:t)["page"+L(n?"Y":"X")]}function F(t){return"undefined"!=typeof TouchEvent&&t instanceof TouchEvent}function H(t){l=t}return{mount:function(){g(_,ee,c,le),g(_,ie,c,le),g(_,ne,O,le),g(_,"click",V,{capture:!0}),g(_,"dragstart",Y),p([gt,wt],D)},disable:H,isDragging:function(){return u}}},Keyboard:function(t,n,e){var i,r,o=Kt(t),s=o.on,u=o.bind,c=o.unbind,l=t.root,f=n.Direction.resolve;function d(){var t=e.keyboard;t&&(i="global"===t?window:l,u(i,he,v))}function h(){c(i,he)}function p(){var t=r;r=!0,a((function(){r=t}))}function v(n){if(!r){var e=de(n);e===f($t)?t.go("<"):e===f(tn)&&t.go(">")}}return{mount:function(){d(),s(wt,h),s(wt,d),s(Et,p)},destroy:h,disable:function(t){r=t}}},LazyLoad:function(t,n,e){var i=Kt(t),r=i.on,s=i.off,a=i.bind,c=i.emit,l="sequential"===e.lazyLoad,f=[St,kt],d=[];function h(){o(d),n.Slides.forEach((function(t){K(t.slide,ge).forEach((function(n){var i=F(n,pe),r=F(n,ve);if(i!==n.src||r!==n.srcset){var o=e.classes.spinner,s=n.parentElement,u=x(s,"."+o)||M("span",o,s);d.push([n,t,u]),n.src||z(n,"none")}}))})),l?m():(s(f),r(f,p),p())}function p(){(d=d.filter((function(n){var i=e.perPage*((e.preloadPages||1)+1)-1;return!n[1].isWithin(t.index,i)||v(n)}))).length||s(f)}function v(t){var n=t[0];C(t[1].slide,Zn),a(n,"load error",u(g,t)),I(n,"src",F(n,pe)),I(n,"srcset",F(n,ve)),V(n,pe),V(n,ve)}function g(t,n){var e=t[0],i=t[1];q(i.slide,Zn),"error"!==n.type&&(j(t[2]),z(e,""),c(jt,e,i),c(Tt)),l&&m()}function m(){d.length&&v(d.shift())}return{mount:function(){e.lazyLoad&&(h(),r(Lt,h))},destroy:u(o,d),check:p}},Pagination:function(t,n,e){var i,r,a=Kt(t),c=a.on,l=a.emit,f=a.bind,d=n.Slides,h=n.Elements,p=n.Controller,v=p.hasFocus,g=p.getIndex,m=p.go,E=n.Direction.resolve,S=h.pagination,y=[];function b(){i&&(j(S?s(i.children):i),q(i,r),o(y),i=null),a.destroy()}function _(t){m(">"+t,!0)}function A(t,n){var e=y.length,i=de(n),r=L(),o=-1;i===E(tn,!1,r)?o=++t%e:i===E($t,!1,r)?o=(--t+e)%e:"Home"===i?o=0:"End"===i&&(o=e-1);var s=y[o];s&&(W(s.button),m(">"+o),Y(n,!0))}function L(){return e.paginationDirection||e.direction}function w(t){return y[p.toPage(t)]}function T(){var t=w(g(!0)),n=w(g());if(t){var e=t.button;q(e,Yn),V(e,pn),I(e,ln,-1)}if(n){var r=n.button;C(r,Yn),I(r,pn,!0),I(r,ln,"")}l(zt,{list:i,items:y},t,n)}return{items:y,mount:function n(){b(),c([wt,Lt,Xt],n);var o=e.pagination;S&&z(S,o?"":"none"),o&&(c([Et,Ot,kt],T),function(){var n=t.length,o=e.classes,s=e.i18n,a=e.perPage,c=v()?p.getEnd()+1:rt(n/a);C(i=S||M("ul",o.pagination,h.track.parentElement),r=In+"--"+L()),I(i,cn,"tablist"),I(i,vn,s.select),I(i,En,L()===sn?"vertical":"");for(var l=0;l<c;l++){var g=M("li",null,i),m=M("button",{class:o.page,type:"button"},g),E=d.getIn(l).map((function(t){return t.slide.id})),b=!v()&&a>1?s.pageX:s.slideX;f(m,"click",u(_,l)),e.paginationKeyboard&&f(m,"keydown",u(A,l)),I(g,cn,"presentation"),I(m,cn,"tab"),I(m,dn,E.join(" ")),I(m,vn,ft(b,l+1)),I(m,ln,-1),y.push({li:g,button:m,page:l})}}(),T(),l(Gt,{list:i,items:y},w(t.index)))},destroy:b,getAt:w,update:T}},Sync:function(t,n,e){var i=e.isNavigation,r=e.slideFocus,s=[];function a(){var n,e;t.splides.forEach((function(n){n.isParent||(l(t,n.splide),l(n.splide,t))})),i&&((e=(n=Kt(t)).on)(yt,d),e(Bt,h),e([gt,wt],f),s.push(n),n.emit(Wt,t.splides))}function c(){s.forEach((function(t){t.destroy()})),o(s)}function l(t,n){var e=Kt(t);e.on(Et,(function(t,e,i){n.go(n.is(oe)?i:t)})),s.push(e)}function f(){I(n.Elements.list,En,e.direction===sn?"vertical":"")}function d(n){t.go(n.index)}function h(t,n){b(me,de(n))&&(d(t),Y(n))}return{setup:u(n.Media.set,{slideFocus:g(r)?i:r},!0),mount:a,destroy:c,remount:function(){c(),a()}}},Wheel:function(t,n,e){var i=Kt(t).bind,r=0;function o(i){if(i.cancelable){var o=i.deltaY,s=o<0,u=Z(i),a=e.wheelMinThreshold||0,c=e.wheelSleep||0;ot(o)>a&&u-r>c&&(t.go(s?"<":">"),r=u),function(i){return!e.releaseWheel||t.state.is(4)||-1!==n.Controller.getAdjacent(i)}(s)&&Y(i)}}return{mount:function(){e.wheel&&i(n.Elements.track,"wheel",o,le)}}},Live:function(t,n,e){var i=Kt(t).on,r=n.Elements.track,o=e.live&&!e.isNavigation,s=M("span",jn),a=qt(90,u(c,!1));function c(t){I(r,bn,t),t?(L(r,s),a.start()):(j(s),a.cancel())}function l(t){o&&I(r,yn,t?"off":"polite")}return{mount:function(){o&&(l(!n.Autoplay.isPaused()),I(r,_n,!0),s.textContent="…",i(Ft,u(l,!0)),i(Ut,u(l,!1)),i([St,kt],u(c,!0)))},disable:l,destroy:function(){V(r,[yn,_n,bn]),j(s)}}}}),Se={type:"slide",role:"region",speed:400,perPage:1,cloneStatus:!0,arrows:!0,pagination:!0,paginationKeyboard:!0,interval:5e3,pauseOnHover:!0,pauseOnFocus:!0,resetProgress:!0,easing:"cubic-bezier(0.25, 1, 0.5, 1)",drag:!0,direction:"ltr",trimSpace:!0,focusableNodes:"a, button, textarea, input, select, iframe",live:!0,classes:te,i18n:{prev:"Previous slide",next:"Next slide",first:"Go to first slide",last:"Go to last slide",slideX:"Go to slide %s",pageX:"Go to page %s",play:"Start autoplay",pause:"Pause autoplay",carousel:"carousel",slide:"slide",select:"Select a slide to show",slideLabel:"%s of %s"},reducedMotion:{speed:0,rewindSpeed:0,autoplay:"pause"}};function ye(t,n,e){var i=n.Slides;function r(){i.forEach((function(t){t.style("transform","translateX(-"+100*t.index+"%)")}))}return{mount:function(){Kt(t).on([gt,Lt],r)},start:function(t,n){i.style("transition","opacity "+e.speed+"ms "+e.easing),a(n)},cancel:c}}function be(t,n,e){var i,r=n.Move,o=n.Controller,s=n.Scroll,a=n.Elements.list,c=u(G,a,"transition");function l(){c(""),s.cancel()}return{mount:function(){Kt(t).bind(a,"transitionend",(function(t){t.target===a&&i&&(l(),i())}))},start:function(n,u){var a=r.toPosition(n,!0),l=r.getPosition(),f=function(n){var i=e.rewindSpeed;if(t.is(re)&&i){var r=o.getIndex(!0),s=o.getEnd();if(0===r&&n>=s||r>=s&&0===n)return i}return e.speed}(n);ot(a-l)>=1&&f>=1?e.useScroll?s.scroll(a,f,!1,u):(c("transform "+f+"ms "+e.easing),r.translate(a,!0),i=u):(r.jump(n),u())},cancel:l}}var _e=function(){function t(n,e){this.event=Kt(),this.Components={},this.state=Zt(1),this.splides=[],this._o={},this._E={};var i=v(n)?X(document,n):n;tt(i,i+" is invalid."),this.root=i,e=k({label:F(i,vn)||"",labelledby:F(i,gn)||""},Se,t.defaults,e||{});try{k(e,JSON.parse(F(i,$)))}catch(t){tt(!1,"Invalid JSON")}this._o=Object.create(k({},e))}var n,e,r=t.prototype;return r.mount=function(t,n){var e=this,i=this.state,r=this.Components;return tt(i.is([1,7]),"Already mounted!"),i.set(1),this._C=r,this._T=n||this._T||(this.is(se)?ye:be),this._E=t||this._E,D(O({},Ee,this._E,{Transition:this._T}),(function(t,n){var i=t(e,r,e._o);r[n]=i,i.setup&&i.setup()})),D(r,(function(t){t.mount&&t.mount()})),this.emit(gt),C(this.root,Bn),i.set(3),this.emit(mt),this},r.sync=function(t){return this.splides.push({splide:t}),t.splides.push({splide:this,isParent:!0}),this.state.is(3)&&(this._C.Sync.remount(),t.Components.Sync.remount()),this},r.go=function(t){return this._C.Controller.go(t),this},r.on=function(t,n){return this.event.on(t,n),this},r.off=function(t){return this.event.off(t),this},r.emit=function(t){var n;return(n=this.event).emit.apply(n,[t].concat(s(arguments,1))),this},r.add=function(t,n){return this._C.Slides.add(t,n),this},r.remove=function(t){return this._C.Slides.remove(t),this},r.is=function(t){return this._o.type===t},r.refresh=function(){return this.emit(Lt),this},r.destroy=function(t){void 0===t&&(t=!0);var n=this.event,e=this.state;return e.is(1)?Kt(this).on(mt,this.destroy.bind(this,t)):(D(this._C,(function(n){n.destroy&&n.destroy(t)}),!0),n.emit(Vt),n.destroy(),t&&o(this.splides),e.set(7)),this},n=t,(e=[{key:"options",get:function(){return this._o},set:function(t){this._C.Media.set(t,!0,!0)}},{key:"length",get:function(){return this._C.Slides.getLength(!0)}},{key:"index",get:function(){return this._C.Controller.getIndex()}}])&&i(n.prototype,e),Object.defineProperty(n,"prototype",{writable:!1}),t}();_e.defaults={},_e.STATES={CREATED:1,MOUNTED:2,IDLE:3,MOVING:4,SCROLLING:5,DRAGGING:6,DESTROYED:7};var Ae={listTag:"ul",slideTag:"li"},Ce=function(){function t(t,n){this.styles={},this.id=t,this.options=n}var n=t.prototype;return n.rule=function(t,n,e,i){i=i||"default";var r=this.styles[i]=this.styles[i]||{};(r[t]=r[t]||{})[n]=e},n.build=function(){var t=this,n="";return this.styles.default&&(n+=this.buildSelectors(this.styles.default)),Object.keys(this.styles).sort((function(n,e){return"min"===t.options.mediaQuery?+n-+e:+e-+n})).forEach((function(e){"default"!==e&&(n+="@media screen and (max-width: "+e+"px) {",n+=t.buildSelectors(t.styles[e]),n+="}")})),n},n.buildSelectors=function(t){var n=this,e="";return D(t,(function(t,i){i=("#"+n.id+" "+i).trim(),e+=i+" {",D(t,(function(t,n){(t||0===t)&&(e+=n+": "+t+";")})),e+="}"})),e},t}(),Le=function(){function t(t,n,e,i){this.slides=[],this.options={},this.breakpoints=[],k(Se,i||{}),k(k(this.options,Se),n||{}),this.contents=t,this.config=O({},Ae,e||{}),this.id=this.config.id||pt("splide"),this.Style=new Ce(this.id,this.options),this.Direction=an(0,0,this.options),tt(this.contents.length,"Provide at least 1 content."),this.init()}t.clean=function(t){var n=Kt(t).on,e=t.root,i=K(e,"."+Pn);n(gt,(function(){j(x(e,"style"))})),j(i)};var n=t.prototype;return n.init=function(){this.parseBreakpoints(),this.initSlides(),this.registerRootStyles(),this.registerTrackStyles(),this.registerSlideStyles(),this.registerListStyles()},n.initSlides=function(){var t=this;_(this.slides,this.contents.map((function(n,e){(n=v(n)?{html:n}:n).styles=n.styles||{},n.attrs=n.attrs||{},t.cover(n);var i=t.options.classes.slide+" "+(0===e?Yn:"");return O(n.attrs,{class:(i+" "+(n.attrs.class||"")).trim(),style:t.buildStyles(n.styles)}),n}))),this.isLoop()&&this.generateClones(this.slides)},n.registerRootStyles=function(){var t=this;this.breakpoints.forEach((function(n){var e=n[0],i=n[1];t.Style.rule(" ","max-width",J(i.width),e)}))},n.registerTrackStyles=function(){var t=this,n=this.Style,e="."+Tn;this.breakpoints.forEach((function(i){var r=i[0],o=i[1];n.rule(e,t.resolve("paddingLeft"),t.cssPadding(o,!1),r),n.rule(e,t.resolve("paddingRight"),t.cssPadding(o,!0),r),n.rule(e,"height",t.cssTrackHeight(o),r)}))},n.registerListStyles=function(){var t=this,n=this.Style,e="."+Nn;this.breakpoints.forEach((function(i){var r=i[0],o=i[1];n.rule(e,"transform",t.buildTranslate(o),r),t.cssSlideHeight(o)||n.rule(e,"aspect-ratio",t.cssAspectRatio(o),r)}))},n.registerSlideStyles=function(){var t=this,n=this.Style,e="."+xn;this.breakpoints.forEach((function(i){var r=i[0],o=i[1];n.rule(e,"width",t.cssSlideWidth(o),r),n.rule(e,"height",t.cssSlideHeight(o)||"100%",r),n.rule(e,t.resolve("marginRight"),J(o.gap)||"0px",r),n.rule(e+" > img","display",o.cover?"none":"inline",r)}))},n.buildTranslate=function(t){var n=this.Direction,e=n.resolve,i=n.orient,r=[];return r.push(this.cssOffsetClones(t)),r.push(this.cssOffsetGaps(t)),this.isCenter(t)&&(r.push(this.buildCssValue(i(-50),"%")),r.push.apply(r,this.cssOffsetCenter(t))),r.filter(Boolean).map((function(t){return"translate"+e("X")+"("+t+")"})).join(" ")},n.cssOffsetClones=function(t){var n=this.Direction,e=n.resolve,i=n.orient,r=this.getCloneCount();if(this.isFixedWidth(t)){var o=this.parseCssValue(t[e("fixedWidth")]),s=o.value,u=o.unit;return this.buildCssValue(i(s)*r,u)}return i(100*r/t.perPage)+"%"},n.cssOffsetCenter=function(t){var n=this.Direction,e=n.resolve,i=n.orient;if(this.isFixedWidth(t)){var r=this.parseCssValue(t[e("fixedWidth")]),o=r.value,s=r.unit;return[this.buildCssValue(i(o/2),s)]}var u=[],a=t.perPage,c=t.gap;if(u.push(i(50/a)+"%"),c){var l=this.parseCssValue(c),f=l.value,d=l.unit,h=(f/a-f)/2;u.push(this.buildCssValue(i(h),d))}return u},n.cssOffsetGaps=function(t){var n=this.getCloneCount();if(n&&t.gap){var e=this.Direction.orient,i=this.parseCssValue(t.gap),r=i.value,o=i.unit;if(this.isFixedWidth(t))return this.buildCssValue(e(r*n),o);var s=n/t.perPage;return this.buildCssValue(e(s*r),o)}return""},n.resolve=function(t){return lt(this.Direction.resolve(t))},n.cssPadding=function(t,n){var e=t.padding,i=this.Direction.resolve(n?"right":"left",!0);return e&&J(e[i]||(d(e)?0:e))||"0px"},n.cssTrackHeight=function(t){var n="";return this.isVertical()&&(tt(n=this.cssHeight(t),'"height" is missing.'),n="calc("+n+" - "+this.cssPadding(t,!1)+" - "+this.cssPadding(t,!0)+")"),n},n.cssHeight=function(t){return J(t.height)},n.cssSlideWidth=function(t){return t.autoWidth?"":J(t.fixedWidth)||(this.isVertical()?"":this.cssSlideSize(t))},n.cssSlideHeight=function(t){return J(t.fixedHeight)||(this.isVertical()?t.autoHeight?"":this.cssSlideSize(t):this.cssHeight(t))},n.cssSlideSize=function(t){var n=J(t.gap);return"calc((100%"+(n&&" + "+n)+")/"+(t.perPage||1)+(n&&" - "+n)+")"},n.cssAspectRatio=function(t){var n=t.heightRatio;return n?""+1/n:""},n.buildCssValue=function(t,n){return""+t+n},n.parseCssValue=function(t){return v(t)?{value:parseFloat(t)||0,unit:t.replace(/\d*(\.\d*)?/,"")||"px"}:{value:t,unit:"px"}},n.parseBreakpoints=function(){var t=this,n=this.options.breakpoints;this.breakpoints.push(["default",this.options]),n&&D(n,(function(n,e){t.breakpoints.push([e,k(k({},t.options),n)])}))},n.isFixedWidth=function(t){return!!t[this.Direction.resolve("fixedWidth")]},n.isLoop=function(){return this.options.type===oe},n.isCenter=function(t){if("center"===t.focus){if(this.isLoop())return!0;if(this.options.type===re)return!this.options.trimSpace}return!1},n.isVertical=function(){return this.options.direction===sn},n.buildClasses=function(){var t=this.options;return[wn,wn+"--"+t.type,wn+"--"+t.direction,t.drag&&wn+"--draggable",t.isNavigation&&wn+"--nav",Yn,!this.config.hidden&&"is-rendered"].filter(Boolean).join(" ")},n.buildAttrs=function(t){var n="";return D(t,(function(t,e){n+=t?" "+lt(e)+'="'+t+'"':""})),n.trim()},n.buildStyles=function(t){var n="";return D(t,(function(t,e){n+=" "+lt(e)+":"+t+";"})),n.trim()},n.renderSlides=function(){var t=this,n=this.config.slideTag;return this.slides.map((function(e){return"<"+n+" "+t.buildAttrs(e.attrs)+">"+(e.html||"")+"</"+n+">"})).join("")},n.cover=function(t){var n=t.styles,e=t.html,i=void 0===e?"":e;if(this.options.cover&&!this.options.lazyLoad){var r=i.match(/<img.*?src\s*=\s*(['"])(.+?)\1.*?>/);r&&r[2]&&(n.background="center/cover no-repeat url('"+r[2]+"')")}},n.generateClones=function(t){for(var n=this.options.classes,e=this.getCloneCount(),i=t.slice();i.length<e;)_(i,i);_(i.slice(-e).reverse(),i.slice(0,e)).forEach((function(i,r){var o=O({},i.attrs,{class:i.attrs.class+" "+n.clone}),s=O({},i,{attrs:o});r<e?t.unshift(s):t.push(s)}))},n.getCloneCount=function(){if(this.isLoop()){var t=this.options;return t.clones?t.clones:et.apply(void 0,this.breakpoints.map((function(t){return t[1].perPage})))*((t.flickMaxPages||1)+1)}return 0},n.renderArrows=function(){var t="";return t+='<div class="'+this.options.classes.arrows+'">',t+=this.renderArrow(!0),(t+=this.renderArrow(!1))+"</div>"},n.renderArrow=function(t){var n=this.options,e=n.classes,i=n.i18n,r={class:e.arrow+" "+(t?e.prev:e.next),type:"button",ariaLabel:t?i.prev:i.next};return"<button "+this.buildAttrs(r)+'><svg xmlns="'+ue+'" viewBox="0 0 40 40" width="40" height="40"><path d="'+(this.options.arrowPath||ae)+'" /></svg></button>'},n.html=function(){var t=this.config,n=t.rootClass,e=t.listTag,i=t.arrows,r=t.beforeTrack,o=t.afterTrack,s=t.slider,u=t.beforeSlider,a=t.afterSlider,c="";return c+='<div id="'+this.id+'" class="'+this.buildClasses()+" "+(n||"")+'">',c+="<style>"+this.Style.build()+"</style>",s&&(c+=u||"",c+='<div class="splide__slider">'),c+=r||"",i&&(c+=this.renderArrows()),c+='<div class="splide__track">',c+="<"+e+' class="splide__list">',c+=this.renderSlides(),c+="</"+e+">",c+="</div>",c+=o||"",s&&(c+="</div>",c+=a||""),c+="</div>"},t}()}}]);
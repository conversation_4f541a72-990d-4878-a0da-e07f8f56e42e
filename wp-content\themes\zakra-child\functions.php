<?php
function zakra_child_enqueue_styles() {
    // Load parent theme CSS
    wp_enqueue_style( 'zakra-parent-style', get_template_directory_uri() . '/style.css' );

    // Load child theme CSS
    wp_enqueue_style( 'zakra-child-style', get_stylesheet_directory_uri() . '/style.css', array('zakra-parent-style') );
}
add_action( 'wp_enqueue_scripts', 'zakra_child_enqueue_styles' );

/**
 * Enqueue custom JavaScript for animations
 */
function zakra_child_enqueue_scripts() {
    wp_enqueue_script(
        'zakra-child-animations',
        get_stylesheet_directory_uri() . '/js/animations.js',
        array('jquery'),
        '1.0.0',
        true
    );
}
add_action( 'wp_enqueue_scripts', 'zakra_child_enqueue_scripts' );

/**
 * Add custom homepage section
 */
function zakra_child_custom_homepage_section() {
    // Only show on front page
    if ( !is_front_page() ) {
        return;
    }
    ?>
    <section class="zakra-custom-section animate-on-scroll">
        <div class="zak-container">
            <div class="zak-row">
                <div class="custom-section-content">
                    <div class="custom-section-text">
                        <h2 class="custom-section-title animate-on-scroll">Welcome to Our Amazing Website</h2>
                        <p class="custom-section-description animate-on-scroll">
                            Experience the power of modern web design with smooth animations and engaging content.
                            This custom section demonstrates how easy it is to extend your WordPress theme.
                        </p>
                        <div class="custom-section-buttons animate-on-scroll">
                            <a href="#" class="custom-btn custom-btn-primary">Get Started</a>
                            <a href="#" class="custom-btn custom-btn-secondary">Learn More</a>
                        </div>
                    </div>
                    <div class="custom-section-image animate-on-scroll">
                        <div class="image-placeholder">
                            <svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="400" height="300" fill="#f3f4f6"/>
                                <circle cx="200" cy="150" r="50" fill="#027ABB" opacity="0.8"/>
                                <circle cx="150" cy="120" r="30" fill="#10b981" opacity="0.6"/>
                                <circle cx="250" cy="180" r="35" fill="#f59e0b" opacity="0.7"/>
                                <text x="200" y="250" text-anchor="middle" fill="#6b7280" font-family="Arial" font-size="14">Custom Section Image</text>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <?php
}

/**
 * Hook the custom section into the homepage
 * Using multiple hooks to ensure it appears
 */
function zakra_child_add_homepage_section() {
    if ( is_front_page() || is_home() ) {
        zakra_child_custom_homepage_section();
    }
}
// Try multiple hooks to ensure the section appears
add_action( 'zakra_content', 'zakra_child_add_homepage_section', 5 );
add_action( 'zakra_action_before_content', 'zakra_child_add_homepage_section', 15 );

/**
 * Temporary fallback - add to wp_footer for testing
 * Remove this once you confirm the main section is working
 */
function zakra_child_test_section() {
    if ( is_front_page() || is_home() ) {
        echo '<div style="background: #f0f0f0; padding: 20px; margin: 20px 0; text-align: center; border: 2px solid #333;">';
        echo '<h2>🎉 Custom Section is Working!</h2>';
        echo '<p>If you see this, the child theme functions are loading correctly.</p>';
        echo '</div>';
    }
}
add_action( 'wp_footer', 'zakra_child_test_section' );

/**
 * Add custom body classes for styling
 */
function zakra_child_body_classes( $classes ) {
    if ( is_front_page() ) {
        $classes[] = 'has-custom-section';
    }
    return $classes;
}
add_filter( 'body_class', 'zakra_child_body_classes' );
?>
=== BlockArt Blocks – Gutenberg Blocks, Page Builder Blocks ,WordPress Block Plugin, Sections & Template Library  ===
Contributors: WPBlockArt
Tags: gutenberg, gutenberg blocks, block editor, editor, page builder
Requires at least: 5.5
Tested up to: 6.8
Requires PHP: 7.4
Stable tag: 2.2.10
License: GPLv3
License URI: http://www.gnu.org/licenses/gpl-3.0.html

Enhance the power of your WordPress editor with the dynamic Gutenberg blocks by BlockArt Blocks. Build any layout imaginable.

== Description ==

### BLOCKART BLOCKS

👉 [BlockArt Blocks Site](https://wpblockart.com/blockart-blocks/)
👉 [Documentation](https://docs.wpblockart.com/blockart-blocks/)
👉 [Roadmap](https://blockart.productlift.dev/)
👉 [Give a Try](https://tastewp.com/template/blockartblocks) (Clicking this link creates a demo where you can test BlockArt Blocks.)


### AN EXEMPLARY WORDPRESS GUTENBERG BLOCKS PLUGIN - BLOCKART BLOCKS

Say goodbye to the era of coding. BlockArt Blocks is the ultimate WordPress Gutenberg blocks plugin, revolutionizing the creation of your dream website in the easiest and fastest way possible.

With its versatile array of unique blocks, BlockArt Blocks enables you to reflect your creativity on the site just the way you want. The best part is you can also enable or disable the blocks as per your requirements.

Further, crafting stunning pages is effortless with a collection of 52 pre-built sections, including banners, calls-to-action, contact forms, content layouts, countdown timers, and more. Plus, there are 7 starter packs tailored for business and non-profit websites. Additionally, you’ll find 24 exquisite templates to enhance your website's design journey.

This Gutenberg plugin also offers an intuitive interface. Thus, it lets you easily import pre-built sections, starter packs, and templates.

So, are you ready to elevate your website building journey to a new height with BlockArt Blocks - a powerful Gutenberg blocks plugin?

https://www.youtube.com/watch?v=_aQoIIbcwmM

### 25+ CUSTOM GUTENBERG BLOCKS

Choose from a diverse range of 29 flexible Gutenberg blocks. You can effortlessly toggle individual blocks on or off with the modular control.

* **Section:** Add rows and columns to your page to create layout variations. You can insert any other blocks of your choice into these columns and make your page unique.
* **Heading:** Create stylish titles with various markups from H1 to H6.
* **Paragraph:** Share texts and information seamlessly with viewers. You can personalize typography and style text with various customization options.
* **Button:** Redirect users to other websites/webpages by assigning a block with a specific URL. Also, you can incorporate multiple buttons for various purposes.
* **Image:** Enhance your website’s visual appeal by inserting relevant images or photographs.
* **Spacing:** Maintain a clutter-free and clean page by creating gaps between blocks.
* **Social Share:** Allow users to share your content across their social profiles with just one click.
* **Tabs:** Add and display your website’s content in different tabs on your page.
* **Table of Content:** Provide a structured overview of content for easy navigation.
* **Counter:** Display your statistics in numbers in various styles.
* **Lottie Animation:** Upload Lottie animations to your webpage to enhance your content.
* **Team:** Add the team block to showcase your team members and their descriptions.
* **Countdown:** Display a countdown for a particular occasion or event on your website.
* **Info Box:** Display customizable and informative boxes with titles, paragraphs, and buttons to redirect users to other important pages.
* **Slider:**  Add beautiful and responsive sliders to showcase elements like text, images, videos, and more on your page.
* **Blockquote:** Add inspiring and motivational quotes alongside the name of the person who coined it.
* **Timeline:** Display your history or other important events in chronological order.
* **Notice:** Highlight important and urgent news, announcements, and other content that requires users’ immediate attention.
* **Progress:** Display progress for specific tasks, projects, and more on an attractive bar scale from 0-100.
* **Call to Action:** Prompt your users to go through the link with text and a clickable button.
* **Google Map:** Insert a map onto your webpage to inform your clients about your location.
* **Testimonial:** Add a slider featuring multiple testimonials to foster trust and credibility with customers.
* **Icon:** Insert 50+ customizable icons to your pages and add links to them to direct users to another page.
* **Icon List:** Create a list and add suitable icons, making your presentation more unique.
* **Modal:** Integrate a pop-up modal window to display additional information and a clickable button to redirect users to the specific page.
* **Image Comparison:** Create a seamless interface for comparing two images side by side.
* **FAQ:** Display answers to common questions from your users in organized drop-down sections.
* **Price List:** Showcase product listings with a short description, price, and a button for each.

### READY-TO-USE WEBSITE DEMOS
With BlockArt Blocks, you get multiple ready-to-import demo sections to enhance your website’s look. There are 52 different sections tailored for the following categories.

* Banner
* CTA
* Contact
* Content
* CountDown
* Feature
* Testimonial
* Team
* Service
* Project
* Pricing
* Gallery
* Other

In addition, you’ll find 24 distinct Gutenberg block templates designed for various pages listed below.

* Home
* About Us
* Contact
* Portfolio
* Causes
* Gallery
* Work
* Destination

Further, if you're short on time, use seven pre-built starter packs containing all the necessary page templates to create a complete website swiftly. Currently, you can find starter packs for the following purposes.

* Freelancer
* Product selling
* Charity
* Travel
* Professional
* Wedding
* Spa

This is just the tip of the iceberg. There are additional starter demos in the pipeline catered to your needs for a faster and more effective website-building experience.

### WHAT MAKES BLOCKART BLOCKS STAND OUT AS THE BEST GUTENBERG BLOCKS PLUGIN?

BlockArt Blocks is a Gutenberg page builder plugin meticulously designed for effortless usage with a minimal to no learning curve.

In addition to providing essential custom blocks for Gutenberg, BlockArt Blocks has the following key features for the effortless creation of captivating websites.

**Ready-made Sections, Templates, and Starter Packs**

With an impressive library of BlockArt Blocks’ pre-built sections, templates, and starter packs, you can kick-start your page-building journey like a pro.

* Ready-to-use templates designed by professional UI/UX designers
* Import the required templates with just a click
* Real-time editing

**Highly Customizable**

Each essential WordPress block provided by the BlockArt Blocks is fully customizable.

* Tailor each block to your requirements with a bunch of general, style, and advanced setting options.

**Optimized for Speed**

BlockArt Blocks is a lightweight and fast Gutenberg blocks plugin. Hence, you can rely on it for quick and fast-loading sites.

* Lightweight blocks designed for optimal performance
* Minimized markup to reduce page size
* Loads Google fonts locally improving site speed and make the site compliant to GDPR
* Compatible with all WordPress caching plugins for improved speed

**Copy and Paste Styles**

BlockArt Blocks allows you to copy and paste styles to various other blocks as required.

* Copy styles that you’ve created previously and paste them anywhere else on the page/post

**Dashboard Widget Area and Customizer Widget Editor**

Incorporate the blocks anywhere, including the dashboard widget and customizer widget areas.

* Works perfectly just like in page editor

**Responsive Editing**

BlockArt Blocks provides fully responsive blocks that allow you to maintain the beauty of your website on any device.

* Adapts perfectly to different devices and screen sizes
* Various setting options to control individual blocks’ width, size, type, height, and more

### ADDITIONAL FEATURES OF BLOCKART BLOCKS
Here are some additional features offered by the best Gutenberg page builder plugin.

* **Multiple Layout Possibilities**: Create dozens of different layouts for your website. With so many blocks, demos, and customization options, your creativity is the only limitation.

* **Customize Measurements with Four Units**: Change measurements for blocks with four types of measurements, i.e., px, rem, em, and percentage.

* **Change the Colors of Blocks and Their Background**: Choose from a wide range of colors for the blocks and their background. You can choose colors to apply to regular and hover conditions.

* **Enable Box Shadow**: Display shadows of blocks with different types of colors. You can also customize the shadow's length, blur, spread, and position.

* **Dozens of Icons to Add to Buttons**: Add icons on the buttons to make them look attractive and catchy. You can also configure their size and gap manually.

* **Default Section Width**: Easily set the default content section width to maintain consistency throughout your website.

* **Responsive Alignment**: Ensure that your website maintains its size and beauty on all devices.

* **Responsive Breakpoints**: Set the responsive breakpoints to match your site’s requirements.

* **Maintenance Mode**: Enable the ‘Maintenance Mode’ if you temporarily take down your site or choose ‘Coming Soon’ for the pre-launch announcement.

* **Option to Insert Additional CSS Classes**: Add CSS classes to the blocks for your personal touch.

### BlockArt Blocks Compatibility with WordPress Themes

BlockArt Blocks is compatible with all the themes built following WordPress standards. However, this Gutenberg block plugin works seamlessly with the following themes.

* [Zakra](https://zakratheme.com/)
* [ColorMag](https://themegrill.com/themes/colormag/)
* Kadence
* Astra
* Neve
* GeneratePress
* OceanWP

### BLOCKART BLOCKS COMPATIBILITY WITH WORDPRESS PLUGINS
Like WordPress themes, BlockArt Blocks is compatible with most WordPress plugins. Nevertheless, this Gutenberg plugin integrates seamlessly with the following plugins.

* [Everest Forms](https://everestforms.net/)
* [User Registration](https://wpuserregistration.com/)
* [Masteriyo](https://masteriyo.com/)
* Contact Form 7
* WP Rocket
* W3 Total Cache

### Documentation and Support
👉 Refer to our [Documentation](https://docs.wpblockart.com/) for self-help.
👉 Have more questions, visit our Plugin’s [Support Forum](https://wordpress.org/support/plugin/blockart-blocks/).
👉 Visit our [Website](https://wpblockart.com/) to contact us directly via the contact form.
👉 We’d love to hear your feedback. Please consider [leaving us a rating](https://wpblockart.com/)!.

### GET IN TOUCH WITH US
👉 [Facebook Group](https://www.facebook.com/groups/themegrill)
👉 [Twitter](http://twitter.com/themegrill)
👉 [YouTube](https://www.youtube.com/@ThemeGrillOfficial)

### EXPLORE MORE PRODUCTS FROM OUR TEAM
Love BlockArt Blocks? If so, we recommend you check out our other WordPress themes and plugins.

 * [Masteriyo](https://masteriyo.com/) A Powerful and Easy WordPress LMS Plugin.
 * [User Registration](https://wpuserregistration.com/) #1 Best WordPress User Registration Plugin.
 * [Magazine Blocks](https://wpblockart.com/magazine-blocks/) Powerful Gutenberg Blocks to Create Magazine Styled Websites.
 * [Zakra](https://zakratheme.com/) Best WordPress Multipurpose Theme Powering 60K+ Websites.
 * [ColorMag](https://themegrill.com/themes/colormag/) #1 Magazine & News Style WordPress Theme.
 * [Everest Forms](https://everestforms.net/) Flexible WordPress Form Builder Plugin.

== Frequently Asked Questions ==

= Is BlockArt plugin free?

Yes! The plugin is completely free of cost. You can download, install, and start creating your website using BlockArt.

= Does it work with any WordPress theme?

Yes! It works with any theme that has been created following the WordPress coding standards.

= Do I need coding skills to create a website using BlockArt?

No! You don’t need to know a single line of code to create awesome websites using BlockArt.

= Can I use this plugin with other block plugins?

Yes! BlockArt is fully compatible with other block plugins.

= Do I need Gutenberg Editor to use BlockArt?

Absolutely Yes! BlockArt has been created using the Gutenberg editor. Hence, it does not go well with classic editor or other page builder plugins.

= What is BlockArt Library?

BlockArt Library is a collection of Sections, Starter Packs, and Templates that you can import to create a page/post.

== Changelog ==
= 2.2.10 - 15-07-2025 =
* Fix - PHP Error.

= 2.2.9 - 01-07-2025 =
* Added - Link Option to Timeline Block.
* Fix - Make links clickable on Tab Block.
* Fix - Url Input field width overflow.

= 2.2.8 - 04-06-2025 =
* Added - Link Option to FAQ content.
* Fix   - Responsive condition issue in modal block.
* Fix   - Alignment issue in Info block.
* Fix   - Replace top margin with bottom margin for dots in arrows and dots style settings of Slider block.

= 2.2.7 - 24-03-2025 =
* Added - Option to change icon color, size, opacity and rotation of items on icon list item Block.
* Added - Item border option on icon list item Block.
* Fix - Icon border color issue on icon list item block
* Added - Option to add icon on overlay figcaption of image block
* Added - Option to change icon color, position, size and gap on overlay figcaption of image block
* Fix - Uploaded image not displaying in editor when editing in Team Block.
* Fix - Flex issue in Section block.

= 2.2.6 - 18-03-2025 =
* Fix - Position conflict for z-index property in heading block.
* Fix - Slider gap.
* Added - Option to add link in Social Share block.
* Added - Button Link feature Style in Teams Block.
* Added - New Query Loop Block.
* Added - Options of color and typography on overlay header and figcaption of image block.
* Added - Border option in question and answer style section of faq block
* Added - Separator position and show separator on option in faq block
* Added - FAQ items margin option in faq block
* Added - Options of hover color on name and designation of Team block.
* Fix - Image overflow issue on hover in Team Block
* Added - Options of hover color, margin, padding and text alignment on overlay header and figcaption of image block

= 2.2.5 - 29-01-2025 =
* Tweak - Update `Tested up to`.

= 2.2.4 - 31-12-2024 =
* Fix - Slider Control.
* Fix - Refine iterator to array for icons.
* Fix - Separators icon render update in Section block.
* Feature - Justify content option in Section block.

= 2.2.3 - 10-12-2024 =
* Fix - Align Icon list item text with icon.
* Fix - Clean routine frequency for cron job.
* Fix - Opacity of the image in image block.
* Fix - Timeline block line position.
* Fix - Icon Block alignment issue.
* Fix - Make slider block mobile responsive.
* Fix - On change value for timeline description.
* Added - Overlay option and blur effect in Column Block.
* Added - Option to add the overlay hover background in Section Block.

= 2.2.2 - 30-08-2024 =
* Fix - Column block width option issue.
* Fix - Paragraph block issue.

= 2.2.1 - 17-07-2024 =
* Added - Option to change row gap in section block.
* Fix - Testimonial block responsive slider issue.
* Fix - Compatibility issue with 6.6 version of WordPress.

= 2.2.0 - 07-07-2024 =
* Fix - Icon list spacing issue.
* Fix - Modal block option issue.
* Fix - Testimonial image control issue.
* Fix - Slider swiper issue.

= 2.1.9 - 21-06-2024 =
* Fix - Image comparison block after image issue.
* Fix - Image comparison label issue.
* Fix - Button border background issue.

= 2.1.8 - 30-05-2024 =
* Fix - Column block issue.

= 2.1.7 - 29-05-2024 =
* Added - Option to change heading block width.
* Added - Option to change column block orientation.
* Added - Option to change column block row gap.
* Added - Option to change column block justification.
* Added - Option to change column block alignment.

= 2.1.6 - 06-05-2024 =
* Added       - Option to change tab title border.
* Enhancement - General security measures.

= 2.1.5 - 19-03-2024 =
* Added - New FAQ block.
* Added - New Image Comparison Block.
* Added - New Price List Block.
* Added - No follow option in button block.
* Fix   - Testimonial Block issue.
* Fix   - Additional classes issue.

= 2.1.4 - 02-02-2024 =
* Fix - Notice block cookie dismiss issue.
* Fix - Style guide design issue.
* Fix - Modal block design issue.
* Fix - Counter block reset issue.
* Fix - Social Share block design issue.

= 2.1.3 - 19-01-2024 =
* Fix - Control design issue.
* Fix - Toc block issue.
* Fix - Global Typography issue.
* Fix - Button block hover color issue.
* Fix - Social share icon block option issue.
* Fix - Tab block background issue.

= 2.1.2 - 16-01-2024 =
* Tweak - Update stable tag.

= 2.1.1 - 16-01-2024 =
* Enhancement - Backward compatibility.

= 2.1.0 - 15-01-2024 =
* Enhancement - Slider Control.
* Enhancement - Dimension Control.
* Enhancement - Color Control.
* Enhancement - Background Control.
* Enhancement - Preset Control.
* Enhancement - Alignment Control.
* Enhancement - Select Control.
* Enhancement - Typography Control.
* Enhancement - Border Control.
* Enhancement - Box shadow Control.
* Enhancement - Icon Picker Control.
* Enhancement - Library modal design.
* Added       - Left Sidebar Panel.
* Added       - Global color and typography setting.

= 2.0.11 - 04-01-2024 =
* Fix - Button block migration.
* Fix - Button block duplicate id issue.

= 2.0.10 - 26-12-2023 =
* Fix - Library search issue .
* Fix - Copy/paste style.
* Fix - CSS generation issue.

= 2.0.9.1 - 06-12-2023 =
* Fix - Js issue.
* Fix - Button block issue.

= 2.0.9 - 06-12-2023 =
* Added - New Modal Block.
* Added - New Icon Block.
* Added - New Icon List Block.
* Fix   - Google map layout issue.
* Fix   - Social share icon gap issue.
* Fix   - List gap and collapsible size issue.
* Fix   - Tab block padding issue.

= 2.0.8 - 23-11-2023 =
* Fix - React compatibility issue.

= 2.0.7.2 - 09-11-2023 =
* Fix   - Asset generation.
* Tweak - Update `Tested up to`.

= 2.0.7 - 09-11-2023 =
* Added   - New Call To Action Block.
* Added   - New Slider Block.
* Added   - New Testimonial Block.
* Added   - New Progress Block.
* Added   - New Blockquote Block.
* Added   - New Notice Block.
* Added   - New Timeline Block.
* Added   - New Map Block.
* Feature – Load Google fonts locally.
* Feature – Preload local fonts.
* Feature – Maintenance mode.
* Feature – Editor options.

= 2.0.6 - 17-10-2023 =
* Fix - Table of contents block markup issue.
* Fix - Countdown number option issue.
* Fix - Typography issue.
* Fix - Copy/Paste style.
* Fix - Background image size and repeat issue.
* Fix - Section vertical alignment issue.

= 2.0.5.2 - 13-10-2023 =
* Fix - Fix php error.

= 2.0.5.1 - 13-10-2023 =
* Fix - Image block width.
* Fix - JS error.

= 2.0.5 - 12-10-2023 =
* Added   - New Tabs Block.
* Added   - New Table of Content Block.
* Added   - New Social Share Block.
* Added   - New Lottie Animation Block.
* Added   - New Counter Block.
* Feature – Option to the counter block alignment.
* Feature – Option to change start number, end number, and decimal places.
* Feature – Option to change prefix, suffix, and thousand separator.
* Feature – Option to change counter icon and icon size.
* Feature – Option to change counter number markup, color, and typography.
* Feature – Option to change counter background, border, and text typography.
* Added   - New Countdown Block.
* Feature – Option to change countdown date and time.
* Feature – Option to change countdown layout.
* Feature – Option to change countdown label character, and customize its color, and typography.
* Feature – Option to change digit color and typography.
* Feature – Option to change separator character, customize its color, and adjust its position.
* Feature – Option to change digit box background, alignment, gap, padding, and border.
* Feature – Option to change countdown block border.
* Added   - New Team Block.
* Feature – Option to change block background and alignment.
* Feature – Option to enable and change team image.
* Feature – Option to enable heading and customize its color, markup, typography, and margin.
* Feature – Option to enable, customize, and style designation, text, including color, typography, and margin.
* Feature – Option to change block border.
* Feature – Option to change social icon and their URL.
* Added   - New Info Box Block.
* Feature – Option to change URL and URL content area.
* Feature – Option to change layout and block background.
* Feature – Option to change icon and its size.
* Feature – Option to enable title, text, button and customize its color, and typography.
* Feature - Additional image block features.
* Fix     - Column icon issue.

= 2.0.4 - 05-10-2023 =
* Fix - Widget block style.
* Fix - Php error.

= 2.0.3 - 29-09-2023 =
* Enhancement: Update google fonts.

= 2.0.2 - 26-09-2023 =
* Fix - button block js error.

= 2.0.1 - 26-09-2023 =
* Feature – Option to add top separator on section block.
* Feature – Option to add bottom separator on section block.
* Feature – Option to change button hover style on button block.
* Feature – Option to add button animation.
* Feature – Option to change button opacity.
* Feature – Option to change button position properties.
* Feature – Option to change button html tag.
* Feature – Option to enable button caption.

= ******* - 20-04-2023 =
* Fix - Block CSS generation issue.

= 2.0.0 - 17-04-2023 =
* Fix           - React 18 render errors.
* Enhancement   - CSS generation for blocks used with block themes.
* Enhancement   - Dynamic CSS filename with timestamp to avoid browser caching issues.

= 1.1.4 - 27-02-2023 =
* Fix         - Flickering issue while using block URL setting.
* FIx         - Block issue while text is selected among multiple blocks.
* Enhancement - Tooltip component.

= ******* - 13-12-2022 =
* Fix - Blocks CSS issue added via customizer.

= 1.1.3 - 08-12-2022 =
* Fix - Block render issue in widget block editor.
* Fix - Block css generation issue in customizer.
* Fix - Possible PHP errors.
* Fix - Button block focus and active state colors issue.

= 1.1.2 - 22-09-2022 =
* Enhancement - Optimize dynamic blocks CSS.
* Fix         - Background image setting not working.

= 1.1.1 - 20-09-2022 =
* Enhancement - Image block on frontend.
* Fix         - Slider block control converting decimal to integer.
* Enhancement - Button block ability to group/stack multiple buttons
* Fix         - CSS generation for blocks.

= 1.1.0 - 05-09-2022 =
* Fix         - Button block icon size and gap settings not working.
* Enhancement - Inspector control tabs and panels.
* Fix         - Widget blocks not generating CSS for latest changes.
* Fix         - Compatibility issue with older version of WordPress.

= 1.0.9 - 23-08-2022 =
* Update - Minimum required WordPress version to 5.5.

= 1.0.8 - 23-08-2022 =
* Fix         - Toolbar deprecated warning.
* Fix         - Library data initial fetch issue.
* Enhancement - Show library data API connection detail error message.

= 1.0.7 - 08-08-2022 =
* Tweak - Image block editor improvement.
* Tweak - Blocks tab inspector control improvement.
* Fix   - Button block icon alignment issue.
* Fix   - Template/Section import issue.

= 1.0.6 - 17-06-2022 =
* Enhancement - Cache library data.

= 1.0.5 - 16-06-2022 =
* Update      - Readme description.
* Enhancement - Library data error handling.

= 1.0.4 - 17-06-2022 =
* Enhancement - BlockArt templates and section library.
* Update      - Header field `Tested up to`.
* Fix         - Possible block recovery issue.
* Fix         - Widget block css issue.

= 1.0.3 - 23-02-2022 =
* Enhancement - Reusable block CSS.
* Added       - Review notice.

= 1.0.2 - 25-01-2022 =
* Fix         - Color palette in WP 5.9.
* Fix         - Section block in WP 5.9.
* Fix         - Dynamic CSS issues on editor in WP 5.9.
* Fix         - Color Picker UI in WP 5.9.
* Fix         - Library modal design in WP 5.9.
* Enhancement - Block layouts.

= 1.0.1 - 12-01-2022 =
* Fix         - Library templates count.
* Enhancement - Section block alignment support.
* Enhancement - Widget block CSS generation and loading on frontend.
* Enhancement - Block settings on widget and customize screens.
* Fix         - Block recovery issue on widget and customize screens.
* Enhancement - Copy and paste styles on older version of WordPress.
* Enhancement - Blocks live preview on customize screen.
* Enhancement - Section block editor CSS.

= 1.0.0 - 02-12-2021 =
* Initial release

(()=>{const{$$:e,domReady:t,toArray:n,each:r,find:o}=window.blockartUtils,a=e=>String(e).padStart(2,"0"),s=e=>{const t=e-(new Date).getTime(),n={days:"00",hours:"00",minutes:"00",seconds:"00"};return t<0||(n.days=a(Math.floor(t/864e5)),n.hours=a(Math.floor(t%864e5/36e5)),n.minutes=a(Math.floor(t%36e5/6e4)),n.seconds=a(Math.floor(t%6e4/1e3))),n};t((()=>{const t=n(e(".blockart-countdown"));t.length&&r(t,(e=>{const t=e.dataset?.expiryTimestamp;if(!t)return;let n=s(parseInt(t));if(Object.values(n).every((e=>"00"===e)))return;const r=setInterval((()=>{n=s(parseInt(t)),Object.values(n).every((e=>"00"===e))&&clearInterval(r);for(const t in n){const r=o(e,`.blockart-countdown-number-${t}`);r&&r.innerHTML!==n[t]&&(r.innerHTML=n[t])}}),1e3)}))}))})();
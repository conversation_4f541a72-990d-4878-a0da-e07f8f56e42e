"use strict";(self.webpackChunkblockart_blocks=self.webpackChunkblockart_blocks||[]).push([[968],{968:(e,t,n)=>{function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function a(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function r(e,t){if(null==e)return{};var n,i,a=function(e,t){if(null==e)return{};var n,i,a={},r=Object.keys(e);for(i=0;i<r.length;i++)n=r[i],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(i=0;i<r.length;i++)n=r[i],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}function o(e,t){var n=t.get(e);if(!n)throw new TypeError("attempted to get private field on non-instance");return n.get?n.get.call(e):n.value}n.r(t),n.d(t,{LottieInteractivity:()=>c,create:()=>C,default:()=>A});var s={player:"lottie-player"},l="[lottieInteractivity]:",c=function(){function e(){var t=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:s,c=n.actions,C=n.container,A=n.mode,T=n.player,H=r(n,["actions","container","mode","player"]);if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),p.set(this,{writable:!0,value:function(){if(t.player){var e=function(){t.player.addEventListener("enterFrame",o(t,g)),t.container.addEventListener("mouseenter",o(t,w)),t.container.addEventListener("mouseleave",o(t,I)),t.container.addEventListener("touchstart",o(t,w),{passive:!0}),t.container.addEventListener("touchend",o(t,I),{passive:!0})},n=function(){t.container.addEventListener("mouseenter",o(t,w)),t.container.addEventListener("mouseleave",o(t,I)),t.container.addEventListener("touchstart",o(t,w),{passive:!0}),t.container.addEventListener("touchend",o(t,I),{passive:!0})};t.stateHandler.set("loop",(function(){t.actions[t.interactionIdx].loop?t.player.loop=parseInt(t.actions[t.interactionIdx].loop)-1:t.player.loop=!0,t.player.autoplay=!0})),t.stateHandler.set("autoplay",(function(){t.player.loop=!1,t.player.autoplay=!0})),t.stateHandler.set("click",(function(){t.player.loop=!1,t.player.autoplay=!1,t.container.addEventListener("click",o(t,d))})),t.stateHandler.set("hover",(function(){t.player.loop=!1,t.player.autoplay=!1,t.container.addEventListener("mouseenter",o(t,d)),t.container.addEventListener("touchstart",o(t,d),{passive:!0})})),t.stateHandler.set("hold",n),t.stateHandler.set("pauseHold",n),t.transitionHandler.set("click",(function(){t.container.addEventListener("click",o(t,y))})),t.transitionHandler.set("hover",(function(){t.container.addEventListener("mouseenter",o(t,y)),t.container.addEventListener("touchstart",o(t,y),{passive:!0})})),t.transitionHandler.set("hold",e),t.transitionHandler.set("pauseHold",e),t.transitionHandler.set("repeat",(function(){t.player.loop=!0,t.player.autoplay=!0,t.player.addEventListener("loopComplete",(function e(){o(t,L).call(t,{handler:e})}))})),t.transitionHandler.set("onComplete",(function(){"loop"===t.actions[t.interactionIdx].state?t.player.addEventListener("loopComplete",o(t,f)):t.player.addEventListener("complete",o(t,f))})),t.transitionHandler.set("seek",(function(){t.player.stop(),t.player.addEventListener("enterFrame",o(t,E)),t.container.addEventListener("mousemove",o(t,u)),t.container.addEventListener("touchmove",o(t,v),{passive:!1}),t.container.addEventListener("mouseout",o(t,m))}))}}}),d.set(this,{writable:!0,value:function(){var e=t.actions[t.interactionIdx].forceFlag;e||!0!==t.player.isPaused?e&&o(t,b).call(t,!0):o(t,b).call(t,!0)}}),h.set(this,{writable:!0,value:function(){0===t.clickCounter?(t.player.play(),t.clickCounter++):(t.clickCounter++,t.player.setDirection(-1*t.player.playDirection),t.player.play())}}),y.set(this,{writable:!0,value:function(){var e=t.actions[t.interactionIdx].forceFlag,n=t.actions[t.interactionIdx].state,i=t.actions[t.interactionIdx].transition;if("chain"===t.mode){if(t.actions[t.interactionIdx].count){var a=parseInt(t.actions[t.interactionIdx].count);if(t.clickCounter<a-1)return void(t.clickCounter+=1)}return t.clickCounter=0,!e&&"click"===i&&"click"===n||"hover"===i&&"hover"===n?t.transitionHandler.get("onComplete").call():t.nextInteraction(),t.container.removeEventListener("click",o(t,y)),void t.container.removeEventListener("mouseenter",o(t,y))}e||!0!==t.player.isPaused?e&&t.player.goToAndPlay(0,!0):t.player.goToAndPlay(0,!0)}}),u.set(this,{writable:!0,value:function(e){o(t,P).call(t,e.clientX,e.clientY)}}),v.set(this,{writable:!0,value:function(e){e.cancelable&&e.preventDefault(),o(t,P).call(t,e.touches[0].clientX,e.touches[0].clientY)}}),m.set(this,{writable:!0,value:function(){o(t,P).call(t,-1,-1)}}),f.set(this,{writable:!0,value:function(){"loop"===t.actions[t.interactionIdx].state?t.player.removeEventListener("loopComplete",o(t,f)):t.player.removeEventListener("complete",o(t,f)),t.nextInteraction()}}),L.set(this,{writable:!0,value:function(e){var n=e.handler,i=1;t.actions[t.interactionIdx].repeat&&(i=t.actions[t.interactionIdx].repeat),t.playCounter>=i-1?(t.playCounter=0,t.player.removeEventListener("loopComplete",n),t.player.loop=!1,t.player.autoplay=!1,t.nextInteraction()):t.playCounter+=1}}),E.set(this,{writable:!0,value:function(){var e=t.actions[t.interactionIdx].frames;e&&t.player.currentFrame>=parseInt(e[1])-1&&(t.player.removeEventListener("enterFrame",o(t,E)),t.container.removeEventListener("mousemove",o(t,u)),t.container.removeEventListener("mouseout",o(t,m)),setTimeout(t.nextInteraction,0))}}),g.set(this,{writable:!0,value:function(){var e=t.actions[t.interactionIdx].frames;(e&&t.player.currentFrame>=e[1]||t.player.currentFrame>=t.player.totalFrames-1)&&(t.player.removeEventListener("enterFrame",o(t,g)),t.container.removeEventListener("mouseenter",o(t,w)),t.container.removeEventListener("mouseleave",o(t,I)),t.container.removeEventListener("touchstart",o(t,w),{passive:!0}),t.container.removeEventListener("touchend",o(t,I),{passive:!0}),t.player.pause(),t.holdStatus=!1,t.nextInteraction()),-1===t.player.playDirection&&e&&t.player.currentFrame<e[0]&&t.player.pause()}}),w.set(this,{writable:!0,value:function(){-1!==t.player.playDirection&&null!==t.holdStatus&&t.holdStatus||(t.player.setDirection(1),t.player.play(),t.holdStatus=!0)}}),I.set(this,{writable:!0,value:function(){"hold"===t.actions[t.interactionIdx].transition||"hold"===t.actions[t.interactionIdx].state||"hold"===t.actions[0].type?(t.player.setDirection(-1),t.player.play()):"pauseHold"!==t.actions[t.interactionIdx].transition&&"pauseHold"!==t.actions[t.interactionIdx].state&&"pauseHold"!==t.actions[0].type||t.player.pause(),t.holdStatus=!1}}),x.set(this,{writable:!0,value:function(){if(t.container.removeEventListener("click",o(t,y)),t.container.removeEventListener("click",o(t,d)),t.container.removeEventListener("mouseenter",o(t,y)),t.container.removeEventListener("touchstart",o(t,y)),t.container.removeEventListener("touchmove",o(t,v)),t.container.removeEventListener("mouseenter",o(t,d)),t.container.removeEventListener("touchstart",o(t,d)),t.container.removeEventListener("mouseenter",o(t,w)),t.container.removeEventListener("touchstart",o(t,w)),t.container.removeEventListener("mouseleave",o(t,I)),t.container.removeEventListener("mousemove",o(t,u)),t.container.removeEventListener("mouseout",o(t,m)),t.container.removeEventListener("touchend",o(t,I)),t.player)try{t.player.removeEventListener("loopComplete",o(t,f)),t.player.removeEventListener("complete",o(t,f)),t.player.removeEventListener("enterFrame",o(t,E)),t.player.removeEventListener("enterFrame",o(t,g))}catch(e){}}}),a(this,"jumpToInteraction",(function(e){o(t,x).call(t),t.interactionIdx=e,t.interactionIdx<0?t.interactionIdx=0:t.interactionIdx,t.nextInteraction(!1)})),a(this,"nextInteraction",(function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];t.oldInterctionIdx=t.interactionIdx,o(t,x).call(t),t.player.loop=!1;var n=t.actions[t.interactionIdx].jumpTo;n?n>=0&&n<t.actions.length?(t.interactionIdx=n,o(t,S).call(t,{ignorePath:!1})):(t.interactionIdx=0,t.player.goToAndStop(0,!0),o(t,S).call(t,{ignorePath:!1})):(e&&t.interactionIdx++,t.interactionIdx>=t.actions.length?t.actions[t.actions.length-1].reset?(t.interactionIdx=0,t.player.resetSegments(!0),t.actions[t.interactionIdx].frames?t.player.goToAndStop(t.actions[t.interactionIdx].frames,!0):t.player.goToAndStop(0,!0),o(t,S).call(t,{ignorePath:!1})):(t.interactionIdx=t.actions.length-1,o(t,S).call(t,{ignorePath:!1})):o(t,S).call(t,{ignorePath:!1})),t.container.dispatchEvent(new CustomEvent("transition",{bubbles:!0,composed:!0,detail:{oldIndex:t.oldInterctionIdx,newIndex:t.interactionIdx}}))})),b.set(this,{writable:!0,value:function(e){var n=t.actions[t.interactionIdx].frames;if(!n)return t.player.resetSegments(!0),void t.player.goToAndPlay(0,!0);"string"==typeof n?t.player.goToAndPlay(n,e):t.player.playSegments(n,e)}}),k.set(this,{writable:!0,value:function(){var e=t.actions[t.interactionIdx].path;if(!e)if("object"===i(t.enteredPlayer)&&"AnimationItem"===t.enteredPlayer.constructor.name){if(e=t.enteredPlayer,t.player===e)return void o(t,S).call(t,{ignorePath:!0})}else{var n=(e=t.loadedAnimation).substr(e.lastIndexOf("/")+1);if(n=n.substr(0,n.lastIndexOf(".json")),t.player.fileName===n)return void o(t,S).call(t,{ignorePath:!0})}var a=t.container.getBoundingClientRect(),r="width: "+a.width+"px !important; height: "+a.height+"px !important; background: "+t.container.style.background;if(t.container.setAttribute("style",r),"object"!==i(t.enteredPlayer)||"AnimationItem"!==t.enteredPlayer.constructor.name){if("string"==typeof t.enteredPlayer){var s=document.querySelector(t.enteredPlayer);s&&"LOTTIE-PLAYER"===s.nodeName&&(t.attachedListeners||(s.addEventListener("ready",(function(){t.container.style.width="",t.container.style.height=""})),s.addEventListener("load",(function(){t.player=s.getLottie(),o(t,S).call(t,{ignorePath:!0})})),t.attachedListeners=!0),s.load(e))}else t.enteredPlayer instanceof HTMLElement&&"LOTTIE-PLAYER"===t.enteredPlayer.nodeName&&(t.attachedListeners||(t.enteredPlayer.addEventListener("ready",(function(){t.container.style.width="",t.container.style.height=""})),t.enteredPlayer.addEventListener("load",(function(){t.player=t.enteredPlayer.getLottie(),o(t,S).call(t,{ignorePath:!0})})),t.attachedListeners=!0),t.enteredPlayer.load(e));if(!t.player)throw new Error("".concat(l," Specified player is invalid."),t.enteredPlayer)}else{if(!window.lottie)throw new Error("".concat(l," A Lottie player is required."));t.stop(),t.container.innerHTML="","object"===i(e)&&"AnimationItem"===e.constructor.name?t.player=window.lottie.loadAnimation({loop:!1,autoplay:!1,animationData:e.animationData,container:t.container}):t.player=window.lottie.loadAnimation({loop:!1,autoplay:!1,path:e,container:t.container}),t.player.addEventListener("DOMLoaded",(function(){t.container.style.width="",t.container.style.height="",o(t,S).call(t,{ignorePath:!0})}))}t.clickCounter=0,t.playCounter=0}}),S.set(this,{writable:!0,value:function(e){var n=e.ignorePath,i=t.actions[t.interactionIdx].frames,a=t.actions[t.interactionIdx].state,r=t.actions[t.interactionIdx].transition,s=t.actions[t.interactionIdx].path,l=t.stateHandler.get(a),c=t.transitionHandler.get(r),p=t.actions[t.interactionIdx].speed?t.actions[t.interactionIdx].speed:1,d=t.actions[t.interactionIdx].delay?t.actions[t.interactionIdx].delay:0;n||!(s||t.actions[t.actions.length-1].reset&&0===t.interactionIdx)?setTimeout((function(){i&&(t.player.autoplay=!1,t.player.resetSegments(!0),t.player.goToAndStop(i[0],!0)),l?l.call():"none"===a&&(t.player.loop=!1,t.player.autoplay=!1),c&&c.call(),t.player.autoplay&&(t.player.resetSegments(!0),o(t,b).call(t,!0)),t.player.setSpeed(p)}),d):o(t,k).call(t)}}),P.set(this,{writable:!0,value:function(e,n){if(-1!==e&&-1!==n){var i=t.getContainerCursorPosition(e,n);e=i.x,n=i.y}var a=t.actions.find((function(t){var i=t.position;if(i){if(Array.isArray(i.x)&&Array.isArray(i.y))return e>=i.x[0]&&e<=i.x[1]&&n>=i.y[0]&&n<=i.y[1];if(!Number.isNaN(i.x)&&!Number.isNaN(i.y))return e===i.x&&n===i.y}return!1}));if(a)if("seek"===a.type||"seek"===a.transition){var r=(e-a.position.x[0])/(a.position.x[1]-a.position.x[0]),o=(n-a.position.y[0])/(a.position.y[1]-a.position.y[0]);t.player.playSegments(a.frames,!0),a.position.y[0]<0&&a.position.y[1]>1?t.player.goToAndStop(Math.floor(r*t.player.totalFrames),!0):t.player.goToAndStop(Math.ceil((r+o)/2*t.player.totalFrames),!0)}else"loop"===a.type?t.player.playSegments(a.frames,!0):"play"===a.type?(!0===t.player.isPaused&&t.player.resetSegments(),t.player.playSegments(a.frames)):"stop"===a.type&&(t.player.resetSegments(!0),t.player.goToAndStop(a.frames[0],!0))}}),M.set(this,{writable:!0,value:function(){var e=t.getContainerVisibility(),n=t.actions.find((function(t){var n=t.visibility;return e>=n[0]&&e<=n[1]}));if(n)if("seek"===n.type){var i=n.frames[0],a=2==n.frames.length?n.frames[1]:t.player.totalFrames-1;null!==t.assignedSegment&&(t.player.resetSegments(!0),t.assignedSegment=null),t.player.goToAndStop(i+Math.round((e-n.visibility[0])/(n.visibility[1]-n.visibility[0])*(a-i)),!0)}else if("loop"===n.type)t.player.loop=!0,(null===t.assignedSegment||t.assignedSegment!==n.frames||!0===t.player.isPaused)&&(t.player.playSegments(n.frames,!0),t.assignedSegment=n.frames);else if("play"===n.type||"playOnce"===n.type){if("playOnce"===n.type&&!t.scrolledAndPlayed)return t.scrolledAndPlayed=!0,t.player.resetSegments(!0),void(n.frames?t.player.playSegments(n.frames,!0):t.player.play());"play"===n.type&&t.player.isPaused&&(t.player.resetSegments(!0),n.frames?t.player.playSegments(n.frames,!0):t.player.play())}else"stop"===n.type&&t.player.goToAndStop(n.frames[0],!0)}}),this.enteredPlayer=T,"object"!==i(T)||"AnimationItem"!==T.constructor.name){if("string"==typeof T){var O=document.querySelector(T);O&&"LOTTIE-PLAYER"===O.nodeName&&(T=O.getLottie())}else T instanceof HTMLElement&&"LOTTIE-PLAYER"===T.nodeName&&(T=T.getLottie());if(!T)throw new Error(l+"Specified player:"+T+" is invalid.")}"string"==typeof C&&(C=document.querySelector(C)),C||(C=T.wrapper),this.player=T,this.loadedAnimation=this.player.path+this.player.fileName+".json",this.attachedListeners=!1,this.container=C,this.mode=A,this.actions=c,this.options=H,this.assignedSegment=null,this.scrolledAndPlayed=!1,this.interactionIdx=0,this.oldInterctionIdx=0,this.clickCounter=0,this.playCounter=0,this.stateHandler=new Map,this.transitionHandler=new Map}var t;return(t=[{key:"getContainerVisibility",value:function(){var e=this.container.getBoundingClientRect(),t=e.top,n=e.height;return(window.innerHeight-t)/(window.innerHeight+n)}},{key:"getContainerCursorPosition",value:function(e,t){var n=this.container.getBoundingClientRect(),i=n.top;return{x:(e-n.left)/n.width,y:(t-i)/n.height}}},{key:"initScrollMode",value:function(){this.player.stop(),window.addEventListener("scroll",o(this,M),!0)}},{key:"initCursorMode",value:function(){this.actions&&1===this.actions.length?"click"===this.actions[0].type?(this.player.loop=!1,this.player.stop(),this.container.addEventListener("click",o(this,y))):"hover"===this.actions[0].type?(this.player.loop=!1,this.player.stop(),this.container.addEventListener("mouseenter",o(this,y)),this.container.addEventListener("touchstart",o(this,y),{passive:!0})):"toggle"===this.actions[0].type?(this.player.loop=!1,this.player.stop(),this.container.addEventListener("click",o(this,h))):"hold"===this.actions[0].type||"pauseHold"===this.actions[0].type?(this.container.addEventListener("mouseenter",o(this,w)),this.container.addEventListener("mouseleave",o(this,I)),this.container.addEventListener("touchstart",o(this,w),{passive:!0}),this.container.addEventListener("touchend",o(this,I),{passive:!0})):"seek"===this.actions[0].type&&(this.player.loop=!0,this.player.stop(),this.container.addEventListener("mousemove",o(this,u)),this.container.addEventListener("touchmove",o(this,v),{passive:!1}),this.container.addEventListener("mouseout",o(this,m))):(this.player.loop=!0,this.player.stop(),this.container.addEventListener("mousemove",o(this,u)),this.container.addEventListener("mouseleave",o(this,m)),o(this,P).call(this,-1,-1))}},{key:"initChainMode",value:function(){o(this,p).call(this),this.player.loop=!1,this.player.stop(),o(this,S).call(this,{ignorePath:!1})}},{key:"start",value:function(){var e=this;"scroll"===this.mode?this.player.isLoaded?this.initScrollMode():this.player.addEventListener("DOMLoaded",(function(){e.initScrollMode()})):"cursor"===this.mode?this.player.isLoaded?this.initCursorMode():this.player.addEventListener("DOMLoaded",(function(){e.initCursorMode()})):"chain"===this.mode&&(this.player.isLoaded?this.initChainMode():this.player.addEventListener("DOMLoaded",(function(){e.initChainMode()})))}},{key:"redefineOptions",value:function(e){var t=e.actions,n=e.container,a=e.mode,o=e.player,s=r(e,["actions","container","mode","player"]);if(this.stop(),this.enteredPlayer=o,"object"!==i(o)||"AnimationItem"!==o.constructor.name){if("string"==typeof o){var c=document.querySelector(o);c&&"LOTTIE-PLAYER"===c.nodeName&&(o=c.getLottie())}else o instanceof HTMLElement&&"LOTTIE-PLAYER"===o.nodeName&&(o=o.getLottie());if(!o)throw new Error(l+"Specified player:"+o+" is invalid.",o)}"string"==typeof n&&(n=document.querySelector(n)),n||(n=o.wrapper),this.player=o,this.loadedAnimation=this.player.path+this.player.fileName+".json",this.attachedListeners=!1,this.container=n,this.mode=a,this.actions=t,this.options=s,this.assignedSegment=null,this.scrolledAndPlayed=!1,this.interactionIdx=0,this.clickCounter=0,this.playCounter=0,this.holdStatus=null,this.stateHandler=new Map,this.transitionHandler=new Map,this.start()}},{key:"stop",value:function(){if("scroll"===this.mode&&window.removeEventListener("scroll",o(this,M),!0),"cursor"===this.mode&&(this.container.removeEventListener("click",o(this,y)),this.container.removeEventListener("click",o(this,h)),this.container.removeEventListener("mouseenter",o(this,y)),this.container.removeEventListener("touchstart",o(this,y)),this.container.removeEventListener("touchmove",o(this,v)),this.container.removeEventListener("mousemove",o(this,u)),this.container.removeEventListener("mouseleave",o(this,m)),this.container.removeEventListener("touchstart",o(this,w)),this.container.removeEventListener("touchend",o(this,I))),"chain"===this.mode&&(this.container.removeEventListener("click",o(this,y)),this.container.removeEventListener("click",o(this,d)),this.container.removeEventListener("mouseenter",o(this,y)),this.container.removeEventListener("touchstart",o(this,y)),this.container.removeEventListener("touchmove",o(this,v)),this.container.removeEventListener("mouseenter",o(this,d)),this.container.removeEventListener("touchstart",o(this,d)),this.container.removeEventListener("mouseenter",o(this,w)),this.container.removeEventListener("touchstart",o(this,w)),this.container.removeEventListener("mouseleave",o(this,I)),this.container.removeEventListener("mousemove",o(this,u)),this.container.removeEventListener("mouseout",o(this,m)),this.container.removeEventListener("touchend",o(this,I)),this.player))try{this.player.removeEventListener("loopComplete",o(this,f)),this.player.removeEventListener("complete",o(this,f)),this.player.removeEventListener("enterFrame",o(this,E)),this.player.removeEventListener("enterFrame",o(this,g))}catch(e){}this.player&&(this.player.destroy(),this.player=null)}}])&&function(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(e.prototype,t),e}(),p=new WeakMap,d=new WeakMap,h=new WeakMap,y=new WeakMap,u=new WeakMap,v=new WeakMap,m=new WeakMap,f=new WeakMap,L=new WeakMap,E=new WeakMap,g=new WeakMap,w=new WeakMap,I=new WeakMap,x=new WeakMap,b=new WeakMap,k=new WeakMap,S=new WeakMap,P=new WeakMap,M=new WeakMap,C=function(e){var t=new c(e);return t.start(),t};const A=C}}]);
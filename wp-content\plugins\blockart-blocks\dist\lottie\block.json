{"$schema": "https://schemas.wp.org/trunk/block.json", "name": "blockart/lottie", "title": "<PERSON><PERSON>", "description": "Display awesome lottie animations to your page.", "keywords": ["lottie", "animation"], "category": "blockart", "textdomain": "blockart", "supports": {"className": false, "customClassName": false}, "example": {"attributes": {}}, "attributes": {"clientId": {"type": "string"}, "source": {"type": "string", "default": "library"}, "playOn": {"type": "string", "default": "auto", "enum": ["auto", "hover", "viewport", "scroll"]}, "loop": {"type": "boolean", "default": true}, "controls": {"type": "boolean", "default": false}, "speed": {"type": "number", "default": 1}, "delay": {"type": "number", "default": 0}, "count": {"type": "number", "default": 0}, "mode": {"type": "string", "default": "normal", "enum": ["normal", "bounce"]}, "lottie": {"type": "object"}, "alignment": {"type": "object", "default": {"desktop": "center"}}, "width": {"type": "number", "default": 200}, "background": {"type": "object", "default": {"background": 1}, "style": [{"selector": "{{WRAPPER}}"}]}, "hoverBackground": {"type": "object", "default": {"background": 1}, "style": [{"selector": "{{WRAPPER}}:hover"}]}, "blockMargin": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { margin: {{VALUE}}; }"}]}, "blockPadding": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { padding: {{VALUE}}; }"}]}, "blockZIndex": {"type": "number", "style": [{"selector": "{{WRAPPER}} { z-index: {{VALUE}}; }"}]}, "cssID": {"type": "string"}, "animation": {"type": "string"}, "interaction": {"type": "object"}, "position": {"type": "object"}, "hideOnDesktop": {"type": "boolean", "style": [{"selector": "@media (min-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnTablet": {"type": "boolean", "style": [{"selector": "@media (min-width:48em) and (max-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} { display: none; } }"}]}, "colReverseOnTablet": {"type": "boolean", "style": [{"selector": "@media (max-width:62em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "colReverseOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "blockCSS": {"type": "string"}, "className": {"type": "string"}}, "style": "blockart-blocks", "editorScript": "blockart-blocks", "editorStyle": "blockart-blocks-editor", "viewScript": "blockart-frontend-lottie"}
{"$schema": "https://schemas.wp.org/trunk/block.json", "name": "blockart/image-gallery", "title": "Image Gallery", "description": "Create customizable gallery of multiple images.", "keywords": ["gallery", "images"], "category": "blockart", "textdomain": "blockart", "supports": {"className": false, "customClassName": false}, "example": {"attributes": {}}, "attributes": {"clientId": {"type": "string"}, "link": {"type": "object", "default": {}}, "images": {"type": "array", "default": []}, "galleryTitle": {"type": "string", "default": "Gallery"}, "columns": {"type": "string", "default": 3}, "layoutType": {"type": "string", "default": "grid"}, "pagination": {"type": "boolean", "default": false}, "pauseOnHover": {"type": "boolean", "default": true}, "arrows": {"type": "boolean", "default": true}, "imgHeight": {"type": "object", "style": [{"selector": ".blockart-image-gallery{{WRAPPER}} .swiper-slide img{height: {{VALUE}}px; }"}]}, "imgWidth": {"type": "object", "style": [{"selector": ".blockart-image-gallery{{WRAPPER}} .swiper-slide img{width: {{VALUE}}px; }"}]}, "imageGap": {"type": "integer", "style": [{"selector": "{{WRAPPER}} .thumbnail-swiper .swiper-slide {margin-left: {{VALUE}}; }"}]}, "thumbnailSliderGap": {"type": "integer", "style": [{"selector": "{{WRAPPER}} .main-swiper {margin-bottom: {{VALUE}}; }"}]}, "thumbnailSliderBorder": {"type": "string", "style": [{"selector": "{{WRAPPER}} .thumbnail-swiper .blockart-image img {border-radius: {{VALUE}}; }"}]}, "mainSliderBorder": {"type": "string", "style": [{"selector": "{{WRAPPER}} .main-swiper .blockart-image img {border-radius: {{VALUE}}; }"}]}, "overlayColor": {"type": "object", "default": {"background": 1, "type": "color"}, "style": [{"selector": "{{WRAPPER}} .swiper-slide"}]}, "thumbnailOverlayColor": {"type": "object", "default": {"background": 1, "type": "color"}, "style": [{"selector": "{{WRAPPER}} .thumbnail-swiper .swiper-slide"}]}, "speed": {"type": "number", "default": 800}, "interval": {"type": "object", "default": 5000}, "autoplay": {"type": "boolean", "default": true}, "loop": {"type": "boolean", "default": false}, "perPage": {"type": "number", "default": 4}, "perMove": {"type": "number", "default": 1}, "buttonAlignment": {"type": "object", "style": [{"selector": "{{WRAPPER}} {text-align: {{VALUE}}; }"}]}, "background": {"type": "object", "default": {"background": 1}, "style": [{"selector": "{{WRAPPER}}"}]}, "enableCaption": {"type": "boolean", "default": false}, "captionLayout": {"type": "string", "default": "overlay"}, "captionVisibility": {"type": "string", "default": "show-on-hover"}, "captionPosition": {"type": "string", "default": "center-center"}, "captionText": {"type": "array", "default": []}, "alt1": {"type": "string"}, "alt2": {"type": "string"}, "width": {"type": "object", "style": [{"selector": "{{WRAPPER}} img { width: {{VALUE}} }"}]}, "maxWidth": {"type": "object", "style": [{"selector": "{{WRAPPER}} img { max-width: {{VALUE}} }"}]}, "height": {"type": "object", "style": [{"selector": "{{WRAPPER}} img { height: {{VALUE}} }"}]}, "opacity": {"type": "string", "default": 1, "style": [{"selector": "{{WRAPPER}} img { opacity: {{VALUE}} }"}]}, "blockMargin": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { margin: {{VALUE}}; }"}]}, "blockPadding": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { padding: {{VALUE}}; }"}]}, "blockZIndex": {"type": "number", "style": [{"selector": "{{WRAPPER}} { z-index: {{VALUE}}; }"}]}, "cssID": {"type": "string"}, "animation": {"type": "string"}, "interaction": {"type": "object"}, "position": {"type": "object"}, "hideOnDesktop": {"type": "boolean", "style": [{"selector": "@media (min-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnTablet": {"type": "boolean", "style": [{"selector": "@media (min-width:48em) and (max-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} { display: none; } }"}]}, "colReverseOnTablet": {"type": "boolean", "style": [{"selector": "@media (max-width:62em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "colReverseOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "blockCSS": {"type": "string"}, "className": {"type": "string"}}, "style": "blockart-blocks", "editorScript": "blockart-blocks", "editorStyle": "blockart-blocks-editor", "viewScript": ["blockart-frontend-image-gallery"]}
{"$schema": "https://schemas.wp.org/trunk/block.json", "name": "blockart/price-list", "title": "Price Lists", "description": "Price Lists.", "keywords": ["price list", "price"], "category": "blockart", "textdomain": "blockart", "supports": {"className": false, "customClassName": false}, "example": {"attributes": {}}, "attributes": {"clientId": {"type": "string"}, "columns": {"type": "number"}, "typography": {"type": "object", "default": {"typography": 1}, "style": [{"selector": ".blockart-price-list{{WRAPPER}}"}]}, "color": {"type": "string", "style": [{"selector": ".blockart-price-list{{WRAPPER}} {color: {{VALUE}}; }"}]}, "hoverColor": {"type": "string", "style": [{"selector": ".blockart-price-list{{WRAPPER}}:hover {color: {{VALUE}}; }"}]}, "background": {"type": "object", "default": {"background": 1}, "style": [{"selector": ".blockart-price-list{{WRAPPER}}"}]}, "hoverBackground": {"type": "object", "default": {"background": 1}, "style": [{"selector": ".blockart-price-list{{WRAPPER}}:hover"}]}, "margin": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": ".blockart-price-list{{WRAPPER}} { margin: {{VALUE}}; }"}]}, "padding": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": ".blockart-price-list{{WRAPPER}} { padding: {{VALUE}}; }"}]}, "blockMargin": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { margin: {{VALUE}}; }"}]}, "blockPadding": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { padding: {{VALUE}}; }"}]}, "blockZIndex": {"type": "number", "style": [{"selector": "{{WRAPPER}} { z-index: {{VALUE}}; }"}]}, "cssID": {"type": "string"}, "animation": {"type": "string"}, "interaction": {"type": "object"}, "position": {"type": "object"}, "hideOnDesktop": {"type": "boolean", "style": [{"selector": "@media (min-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnTablet": {"type": "boolean", "style": [{"selector": "@media (min-width:48em) and (max-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} { display: none; } }"}]}, "colReverseOnTablet": {"type": "boolean", "style": [{"selector": "@media (max-width:62em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "colReverseOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "blockCSS": {"type": "string"}, "className": {"type": "string"}, "countMenuItem": {"type": "number", "default": 3}, "alignment": {"type": "string", "default": "center", "style": [{"selector": "{{WRAPPER}} .blockart-price-item {align-items: {{VALUE}}; text-align: {{VALUE}} } {{WRAPPER}} .blockart-price-item .blockart-price {align-items: {{VALUE}}; text-align: {{VALUE}}; justify-content: {{VALUE}}; }"}]}, "imgPosition": {"type": "string", "default": "bottom"}, "separator": {"type": "boolean", "default": false}, "presets": {"type": "string"}}, "style": "blockart-blocks", "editorScript": "blockart-blocks", "editorStyle": "blockart-blocks-editor"}
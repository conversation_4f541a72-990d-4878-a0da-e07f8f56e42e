(()=>{const{$$:s,domReady:t}=window.blockartUtils;t((()=>{const t=s(".blockart-faq");if(t)for(const s of t){const t=s.classList.contains("expand-first-child"),o=s.classList.contains("collapse-others");t&&s.querySelector(".blockart-control")?.classList.add("is-expanded");const c=s.querySelectorAll(".blockart-faq-title-wrapper");for(const t of c)t.addEventListener("click",(t=>{const c=t.target,e=c?.closest(".blockart-control");if(o&&!e?.classList.contains("is-expanded"))for(const t of s.querySelectorAll(".blockart-control"))t.classList.remove("is-expanded");e?.classList.toggle("is-expanded")}))}}))})();
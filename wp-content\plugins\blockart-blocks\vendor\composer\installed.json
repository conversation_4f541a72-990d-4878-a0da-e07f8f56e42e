{"packages": [{"name": "composer/installers", "version": "v1.11.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/composer/installers.git", "reference": "ae03311f45dfe194412081526be2e003960df74b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/installers/zipball/ae03311f45dfe194412081526be2e003960df74b", "reference": "ae03311f45dfe194412081526be2e003960df74b", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0"}, "replace": {"roundcube/plugin-installer": "*", "shama/baton": "*"}, "require-dev": {"composer/composer": "1.6.* || ^2.0", "composer/semver": "^1 || ^3", "phpstan/phpstan": "^0.12.55", "phpstan/phpstan-phpunit": "^0.12.16", "symfony/phpunit-bridge": "^4.2 || ^5", "symfony/process": "^2.3"}, "time": "2021-04-28T06:42:17+00:00", "type": "composer-plugin", "extra": {"class": "Composer\\Installers\\Plugin", "branch-alias": {"dev-main": "1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Composer\\Installers\\": "src/Composer/Installers"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/shama"}], "description": "A multi-framework Composer library installer", "homepage": "https://composer.github.io/installers/", "keywords": ["Craft", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "ImageCMS", "Kanboard", "Lan Management System", "MODX Evo", "MantisBT", "Mautic", "Maya", "OXID", "Plentymarkets", "Porto", "RadPHP", "SMF", "Starbug", "Thelia", "Whmcs", "WolfCMS", "agl", "aimeos", "annotatecms", "attogram", "bitrix", "cakephp", "chef", "cockpit", "codeigniter", "concrete5", "croogo", "<PERSON><PERSON><PERSON><PERSON>", "drupal", "eZ Platform", "elgg", "expressionengine", "fuelphp", "grav", "installer", "itop", "j<PERSON><PERSON>", "known", "kohana", "laravel", "lavalite", "lithium", "magento", "majima", "mako", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "modulework", "modx", "moodle", "osclass", "phpbb", "piwik", "ppi", "processwire", "puppet", "pxcms", "reindex", "roundcube", "shopware", "silverstripe", "sydes", "sylius", "symfony", "tastyigniter", "typo3", "wordpress", "yawik", "zend", "zikula"], "support": {"issues": "https://github.com/composer/installers/issues", "source": "https://github.com/composer/installers/tree/v1.11.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "install-path": "./installers"}, {"name": "halaxa/json-machine", "version": "1.2.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/halaxa/json-machine.git", "reference": "d0f84abf79ac98145d478b66d2bcf363d706477c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/halaxa/json-machine/zipball/d0f84abf79ac98145d478b66d2bcf363d706477c", "reference": "d0f84abf79ac98145d478b66d2bcf363d706477c", "shasum": ""}, "require": {"php": "7.2 - 8.4"}, "require-dev": {"ext-json": "*", "friendsofphp/php-cs-fixer": "^3.0", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8.0"}, "suggest": {"ext-json": "To run JSON Machine out of the box without custom decoders.", "guzzlehttp/guzzle": "To run example with GuzzleHttp"}, "time": "2025-07-07T13:38:34+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/functions.php"], "psr-4": {"JsonMachine\\": "src/"}, "exclude-from-classmap": ["src/autoloader.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Efficient, easy-to-use and fast JSON pull parser", "support": {"issues": "https://github.com/halaxa/json-machine/issues", "source": "https://github.com/halaxa/json-machine/tree/1.2.5"}, "funding": [{"url": "https://ko-fi.com/G2G57KTE4", "type": "other"}], "install-path": "../halaxa/json-machine"}, {"name": "sabberworm/php-css-parser", "version": "v8.9.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/MyIntervals/PHP-CSS-Parser.git", "reference": "d8e916507b88e389e26d4ab03c904a082aa66bb9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MyIntervals/PHP-CSS-Parser/zipball/d8e916507b88e389e26d4ab03c904a082aa66bb9", "reference": "d8e916507b88e389e26d4ab03c904a082aa66bb9", "shasum": ""}, "require": {"ext-iconv": "*", "php": "^5.6.20 || ^7.0.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "require-dev": {"phpunit/phpunit": "5.7.27 || 6.5.14 || 7.5.20 || 8.5.41", "rawr/cross-data-providers": "^2.0.0"}, "suggest": {"ext-mbstring": "for parsing UTF-8 CSS"}, "time": "2025-07-11T13:20:48+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "9.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Sabberworm\\CSS\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Parser for CSS Files written in PHP", "homepage": "https://www.sabberworm.com/blog/2010/6/10/php-css-parser", "keywords": ["css", "parser", "stylesheet"], "support": {"issues": "https://github.com/MyIntervals/PHP-CSS-Parser/issues", "source": "https://github.com/MyIntervals/PHP-CSS-Parser/tree/v8.9.0"}, "install-path": "../sabberworm/php-css-parser"}], "dev": false, "dev-package-names": []}
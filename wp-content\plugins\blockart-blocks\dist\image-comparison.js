(()=>{var t={};t.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),(()=>{var e;t.g.importScripts&&(e=t.g.location+"");var r=t.g.document;if(!e&&r&&(r.currentScript&&"SCRIPT"===r.currentScript.tagName.toUpperCase()&&(e=r.currentScript.src),!e)){var i=r.getElementsByTagName("script");if(i.length)for(var o=i.length-1;o>-1&&(!e||!/^http(s?):/.test(e));)e=i[o--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/^blob:/,"").replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),t.p=e})(),t.p=window._BLOCKART_WEBPACK_PUBLIC_PATH_,(()=>{const{$$:t,domReady:e,toArray:r,each:i}=window.blockartUtils,o={"orientation-vertical":{linesOrientation:"vertical"}};e((()=>{const e=r(t(".blockart-image-comparison-image-container"));e.length&&i(e,(t=>{const e={container:t,textPosition:"bottom-left",...Object.entries(o).filter((([e])=>t.classList.contains(e))).map((([,t])=>t)).reduce(((t,e)=>({...t,...e})),{})};new Dics(e)}))}))})()})();
<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(dirname(__FILE__));
$baseDir = dirname($vendorDir);

return array(
    'BlockArt\\Abstracts\\Styles' => $baseDir . '/includes/Abstracts/Styles.php',
    'BlockArt\\Activation' => $baseDir . '/includes/Activation.php',
    'BlockArt\\Admin' => $baseDir . '/includes/Admin.php',
    'BlockArt\\BetaTester' => $baseDir . '/includes/BetaTester.php',
    'BlockArt\\BlockArt' => $baseDir . '/includes/BlockArt.php',
    'BlockArt\\BlockStyles' => $baseDir . '/includes/BlockStyles.php',
    'BlockArt\\BlockTypes\\AbstractBlock' => $baseDir . '/includes/BlockTypes/AbstractBlock.php',
    'BlockArt\\BlockTypes\\Blockquote' => $baseDir . '/includes/BlockTypes/Blockquote.php',
    'BlockArt\\BlockTypes\\Button' => $baseDir . '/includes/BlockTypes/Button.php',
    'BlockArt\\BlockTypes\\Buttons' => $baseDir . '/includes/BlockTypes/Buttons.php',
    'BlockArt\\BlockTypes\\CallToAction' => $baseDir . '/includes/BlockTypes/CallToAction.php',
    'BlockArt\\BlockTypes\\Column' => $baseDir . '/includes/BlockTypes/Column.php',
    'BlockArt\\BlockTypes\\Countdown' => $baseDir . '/includes/BlockTypes/Countdown.php',
    'BlockArt\\BlockTypes\\Counter' => $baseDir . '/includes/BlockTypes/Counter.php',
    'BlockArt\\BlockTypes\\FAQ' => $baseDir . '/includes/BlockTypes/FAQ.php',
    'BlockArt\\BlockTypes\\FAQs' => $baseDir . '/includes/BlockTypes/FAQs.php',
    'BlockArt\\BlockTypes\\Heading' => $baseDir . '/includes/BlockTypes/Heading.php',
    'BlockArt\\BlockTypes\\Icon' => $baseDir . '/includes/BlockTypes/Icon.php',
    'BlockArt\\BlockTypes\\IconList' => $baseDir . '/includes/BlockTypes/IconList.php',
    'BlockArt\\BlockTypes\\IconListItem' => $baseDir . '/includes/BlockTypes/IconListItem.php',
    'BlockArt\\BlockTypes\\Image' => $baseDir . '/includes/BlockTypes/Image.php',
    'BlockArt\\BlockTypes\\ImageComparison' => $baseDir . '/includes/BlockTypes/ImageComparison.php',
    'BlockArt\\BlockTypes\\ImageGallery' => $baseDir . '/includes/BlockTypes/ImageGallery.php',
    'BlockArt\\BlockTypes\\Info' => $baseDir . '/includes/BlockTypes/Info.php',
    'BlockArt\\BlockTypes\\Lottie' => $baseDir . '/includes/BlockTypes/Lottie.php',
    'BlockArt\\BlockTypes\\Map' => $baseDir . '/includes/BlockTypes/Map.php',
    'BlockArt\\BlockTypes\\Modal' => $baseDir . '/includes/BlockTypes/Modal.php',
    'BlockArt\\BlockTypes\\Notice' => $baseDir . '/includes/BlockTypes/Notice.php',
    'BlockArt\\BlockTypes\\Paragraph' => $baseDir . '/includes/BlockTypes/Paragraph.php',
    'BlockArt\\BlockTypes\\PostTemplate' => $baseDir . '/includes/BlockTypes/PostTemplate.php',
    'BlockArt\\BlockTypes\\Price' => $baseDir . '/includes/BlockTypes/Price.php',
    'BlockArt\\BlockTypes\\PriceList' => $baseDir . '/includes/BlockTypes/PriceList.php',
    'BlockArt\\BlockTypes\\PriceListChild' => $baseDir . '/includes/BlockTypes/PriceListChild.php',
    'BlockArt\\BlockTypes\\Progress' => $baseDir . '/includes/BlockTypes/Progress.php',
    'BlockArt\\BlockTypes\\QueryLoop' => $baseDir . '/includes/BlockTypes/QueryLoop.php',
    'BlockArt\\BlockTypes\\Section' => $baseDir . '/includes/BlockTypes/Section.php',
    'BlockArt\\BlockTypes\\Slide' => $baseDir . '/includes/BlockTypes/Slide.php',
    'BlockArt\\BlockTypes\\Slider' => $baseDir . '/includes/BlockTypes/Slider.php',
    'BlockArt\\BlockTypes\\SocialInner' => $baseDir . '/includes/BlockTypes/SocialInner.php',
    'BlockArt\\BlockTypes\\SocialShare' => $baseDir . '/includes/BlockTypes/SocialShare.php',
    'BlockArt\\BlockTypes\\Spacing' => $baseDir . '/includes/BlockTypes/Spacing.php',
    'BlockArt\\BlockTypes\\Tab' => $baseDir . '/includes/BlockTypes/Tab.php',
    'BlockArt\\BlockTypes\\TabTitles' => $baseDir . '/includes/BlockTypes/TabTitles.php',
    'BlockArt\\BlockTypes\\TableOfContents' => $baseDir . '/includes/BlockTypes/TableOfContents.php',
    'BlockArt\\BlockTypes\\Tabs' => $baseDir . '/includes/BlockTypes/Tabs.php',
    'BlockArt\\BlockTypes\\Team' => $baseDir . '/includes/BlockTypes/Team.php',
    'BlockArt\\BlockTypes\\Testimonial' => $baseDir . '/includes/BlockTypes/Testimonial.php',
    'BlockArt\\BlockTypes\\TestimonialSlide' => $baseDir . '/includes/BlockTypes/TestimonialSlide.php',
    'BlockArt\\BlockTypes\\Timeline' => $baseDir . '/includes/BlockTypes/Timeline.php',
    'BlockArt\\BlockTypes\\TimelineInner' => $baseDir . '/includes/BlockTypes/TimelineInner.php',
    'BlockArt\\Blocks' => $baseDir . '/includes/Blocks.php',
    'BlockArt\\Deactivation' => $baseDir . '/includes/Deactivation.php',
    'BlockArt\\GlobalStyles' => $baseDir . '/includes/GlobalStyles.php',
    'BlockArt\\Icon' => $baseDir . '/includes/Icon.php',
    'BlockArt\\MaintenanceMode' => $baseDir . '/includes/MaintenanceMode.php',
    'BlockArt\\RestApi\\Controllers\\ChangelogController' => $baseDir . '/includes/RestApi/Controllers/ChangelogController.php',
    'BlockArt\\RestApi\\Controllers\\ImageImportController' => $baseDir . '/includes/RestApi/Controllers/ImageImportController.php',
    'BlockArt\\RestApi\\Controllers\\LibraryDataController' => $baseDir . '/includes/RestApi/Controllers/LibraryDataController.php',
    'BlockArt\\RestApi\\Controllers\\RegenerateAssetsController' => $baseDir . '/includes/RestApi/Controllers/RegenerateAssetsController.php',
    'BlockArt\\RestApi\\Controllers\\SettingsController' => $baseDir . '/includes/RestApi/Controllers/SettingsController.php',
    'BlockArt\\RestApi\\Controllers\\VersionControlController' => $baseDir . '/includes/RestApi/Controllers/VersionControlController.php',
    'BlockArt\\RestApi\\RestApi' => $baseDir . '/includes/RestApi/RestApi.php',
    'BlockArt\\Review' => $baseDir . '/includes/Review.php',
    'BlockArt\\ScriptStyle' => $baseDir . '/includes/ScriptStyle.php',
    'BlockArt\\Setting' => $baseDir . '/includes/Setting.php',
    'BlockArt\\Traits\\Singleton' => $baseDir . '/includes/traits/Singleton.php',
    'BlockArt\\Update' => $baseDir . '/includes/Update.php',
    'BlockArt\\WebFontLoader' => $baseDir . '/includes/WebFontLoader.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Composer\\Installers\\AglInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AglInstaller.php',
    'Composer\\Installers\\AimeosInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AimeosInstaller.php',
    'Composer\\Installers\\AnnotateCmsInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AnnotateCmsInstaller.php',
    'Composer\\Installers\\AsgardInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AsgardInstaller.php',
    'Composer\\Installers\\AttogramInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AttogramInstaller.php',
    'Composer\\Installers\\BaseInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/BaseInstaller.php',
    'Composer\\Installers\\BitrixInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/BitrixInstaller.php',
    'Composer\\Installers\\BonefishInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/BonefishInstaller.php',
    'Composer\\Installers\\CakePHPInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CakePHPInstaller.php',
    'Composer\\Installers\\ChefInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ChefInstaller.php',
    'Composer\\Installers\\CiviCrmInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CiviCrmInstaller.php',
    'Composer\\Installers\\ClanCatsFrameworkInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ClanCatsFrameworkInstaller.php',
    'Composer\\Installers\\CockpitInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CockpitInstaller.php',
    'Composer\\Installers\\CodeIgniterInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CodeIgniterInstaller.php',
    'Composer\\Installers\\Concrete5Installer' => $vendorDir . '/composer/installers/src/Composer/Installers/Concrete5Installer.php',
    'Composer\\Installers\\CraftInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CraftInstaller.php',
    'Composer\\Installers\\CroogoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CroogoInstaller.php',
    'Composer\\Installers\\DecibelInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DecibelInstaller.php',
    'Composer\\Installers\\DframeInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DframeInstaller.php',
    'Composer\\Installers\\DokuWikiInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DokuWikiInstaller.php',
    'Composer\\Installers\\DolibarrInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DolibarrInstaller.php',
    'Composer\\Installers\\DrupalInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DrupalInstaller.php',
    'Composer\\Installers\\ElggInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ElggInstaller.php',
    'Composer\\Installers\\EliasisInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/EliasisInstaller.php',
    'Composer\\Installers\\ExpressionEngineInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ExpressionEngineInstaller.php',
    'Composer\\Installers\\EzPlatformInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/EzPlatformInstaller.php',
    'Composer\\Installers\\FuelInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/FuelInstaller.php',
    'Composer\\Installers\\FuelphpInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/FuelphpInstaller.php',
    'Composer\\Installers\\GravInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/GravInstaller.php',
    'Composer\\Installers\\HuradInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/HuradInstaller.php',
    'Composer\\Installers\\ImageCMSInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ImageCMSInstaller.php',
    'Composer\\Installers\\Installer' => $vendorDir . '/composer/installers/src/Composer/Installers/Installer.php',
    'Composer\\Installers\\ItopInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ItopInstaller.php',
    'Composer\\Installers\\JoomlaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/JoomlaInstaller.php',
    'Composer\\Installers\\KanboardInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/KanboardInstaller.php',
    'Composer\\Installers\\KirbyInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/KirbyInstaller.php',
    'Composer\\Installers\\KnownInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/KnownInstaller.php',
    'Composer\\Installers\\KodiCMSInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/KodiCMSInstaller.php',
    'Composer\\Installers\\KohanaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/KohanaInstaller.php',
    'Composer\\Installers\\LanManagementSystemInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/LanManagementSystemInstaller.php',
    'Composer\\Installers\\LaravelInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/LaravelInstaller.php',
    'Composer\\Installers\\LavaLiteInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/LavaLiteInstaller.php',
    'Composer\\Installers\\LithiumInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/LithiumInstaller.php',
    'Composer\\Installers\\MODULEWorkInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MODULEWorkInstaller.php',
    'Composer\\Installers\\MODXEvoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MODXEvoInstaller.php',
    'Composer\\Installers\\MagentoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MagentoInstaller.php',
    'Composer\\Installers\\MajimaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MajimaInstaller.php',
    'Composer\\Installers\\MakoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MakoInstaller.php',
    'Composer\\Installers\\MantisBTInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MantisBTInstaller.php',
    'Composer\\Installers\\MauticInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MauticInstaller.php',
    'Composer\\Installers\\MayaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MayaInstaller.php',
    'Composer\\Installers\\MediaWikiInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MediaWikiInstaller.php',
    'Composer\\Installers\\MiaoxingInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MiaoxingInstaller.php',
    'Composer\\Installers\\MicroweberInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MicroweberInstaller.php',
    'Composer\\Installers\\ModxInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ModxInstaller.php',
    'Composer\\Installers\\MoodleInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MoodleInstaller.php',
    'Composer\\Installers\\OctoberInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/OctoberInstaller.php',
    'Composer\\Installers\\OntoWikiInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/OntoWikiInstaller.php',
    'Composer\\Installers\\OsclassInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/OsclassInstaller.php',
    'Composer\\Installers\\OxidInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/OxidInstaller.php',
    'Composer\\Installers\\PPIInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PPIInstaller.php',
    'Composer\\Installers\\PhiftyInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PhiftyInstaller.php',
    'Composer\\Installers\\PhpBBInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PhpBBInstaller.php',
    'Composer\\Installers\\PimcoreInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PimcoreInstaller.php',
    'Composer\\Installers\\PiwikInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PiwikInstaller.php',
    'Composer\\Installers\\PlentymarketsInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PlentymarketsInstaller.php',
    'Composer\\Installers\\Plugin' => $vendorDir . '/composer/installers/src/Composer/Installers/Plugin.php',
    'Composer\\Installers\\PortoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PortoInstaller.php',
    'Composer\\Installers\\PrestashopInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PrestashopInstaller.php',
    'Composer\\Installers\\ProcessWireInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ProcessWireInstaller.php',
    'Composer\\Installers\\PuppetInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PuppetInstaller.php',
    'Composer\\Installers\\PxcmsInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PxcmsInstaller.php',
    'Composer\\Installers\\RadPHPInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/RadPHPInstaller.php',
    'Composer\\Installers\\ReIndexInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ReIndexInstaller.php',
    'Composer\\Installers\\Redaxo5Installer' => $vendorDir . '/composer/installers/src/Composer/Installers/Redaxo5Installer.php',
    'Composer\\Installers\\RedaxoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/RedaxoInstaller.php',
    'Composer\\Installers\\RoundcubeInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/RoundcubeInstaller.php',
    'Composer\\Installers\\SMFInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SMFInstaller.php',
    'Composer\\Installers\\ShopwareInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ShopwareInstaller.php',
    'Composer\\Installers\\SilverStripeInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SilverStripeInstaller.php',
    'Composer\\Installers\\SiteDirectInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SiteDirectInstaller.php',
    'Composer\\Installers\\StarbugInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/StarbugInstaller.php',
    'Composer\\Installers\\SyDESInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SyDESInstaller.php',
    'Composer\\Installers\\SyliusInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SyliusInstaller.php',
    'Composer\\Installers\\Symfony1Installer' => $vendorDir . '/composer/installers/src/Composer/Installers/Symfony1Installer.php',
    'Composer\\Installers\\TYPO3CmsInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TYPO3CmsInstaller.php',
    'Composer\\Installers\\TYPO3FlowInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TYPO3FlowInstaller.php',
    'Composer\\Installers\\TaoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TaoInstaller.php',
    'Composer\\Installers\\TastyIgniterInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TastyIgniterInstaller.php',
    'Composer\\Installers\\TheliaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TheliaInstaller.php',
    'Composer\\Installers\\TuskInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TuskInstaller.php',
    'Composer\\Installers\\UserFrostingInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/UserFrostingInstaller.php',
    'Composer\\Installers\\VanillaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/VanillaInstaller.php',
    'Composer\\Installers\\VgmcpInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/VgmcpInstaller.php',
    'Composer\\Installers\\WHMCSInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/WHMCSInstaller.php',
    'Composer\\Installers\\WinterInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/WinterInstaller.php',
    'Composer\\Installers\\WolfCMSInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/WolfCMSInstaller.php',
    'Composer\\Installers\\WordPressInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/WordPressInstaller.php',
    'Composer\\Installers\\YawikInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/YawikInstaller.php',
    'Composer\\Installers\\ZendInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ZendInstaller.php',
    'Composer\\Installers\\ZikulaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ZikulaInstaller.php',
    'JsonMachine\\Exception\\BadMethodCallException' => $vendorDir . '/halaxa/json-machine/src/Exception/BadMethodCallException.php',
    'JsonMachine\\Exception\\InvalidArgumentException' => $vendorDir . '/halaxa/json-machine/src/Exception/InvalidArgumentException.php',
    'JsonMachine\\Exception\\JsonMachineException' => $vendorDir . '/halaxa/json-machine/src/Exception/JsonMachineException.php',
    'JsonMachine\\Exception\\OutOfBoundsException' => $vendorDir . '/halaxa/json-machine/src/Exception/OutOfBoundsException.php',
    'JsonMachine\\Exception\\PathNotFoundException' => $vendorDir . '/halaxa/json-machine/src/Exception/PathNotFoundException.php',
    'JsonMachine\\Exception\\SyntaxErrorException' => $vendorDir . '/halaxa/json-machine/src/Exception/SyntaxErrorException.php',
    'JsonMachine\\Exception\\UnexpectedEndSyntaxErrorException' => $vendorDir . '/halaxa/json-machine/src/Exception/UnexpectedEndSyntaxErrorException.php',
    'JsonMachine\\FacadeTrait' => $vendorDir . '/halaxa/json-machine/src/FacadeTrait.php',
    'JsonMachine\\FileChunks' => $vendorDir . '/halaxa/json-machine/src/FileChunks.php',
    'JsonMachine\\Items' => $vendorDir . '/halaxa/json-machine/src/Items.php',
    'JsonMachine\\ItemsOptions' => $vendorDir . '/halaxa/json-machine/src/ItemsOptions.php',
    'JsonMachine\\JsonDecoder\\DecodingError' => $vendorDir . '/halaxa/json-machine/src/JsonDecoder/DecodingError.php',
    'JsonMachine\\JsonDecoder\\ErrorWrappingDecoder' => $vendorDir . '/halaxa/json-machine/src/JsonDecoder/ErrorWrappingDecoder.php',
    'JsonMachine\\JsonDecoder\\ExtJsonDecoder' => $vendorDir . '/halaxa/json-machine/src/JsonDecoder/ExtJsonDecoder.php',
    'JsonMachine\\JsonDecoder\\InvalidResult' => $vendorDir . '/halaxa/json-machine/src/JsonDecoder/InvalidResult.php',
    'JsonMachine\\JsonDecoder\\ItemDecoder' => $vendorDir . '/halaxa/json-machine/src/JsonDecoder/ItemDecoder.php',
    'JsonMachine\\JsonDecoder\\PassThruDecoder' => $vendorDir . '/halaxa/json-machine/src/JsonDecoder/PassThruDecoder.php',
    'JsonMachine\\JsonDecoder\\StringOnlyDecoder' => $vendorDir . '/halaxa/json-machine/src/JsonDecoder/StringOnlyDecoder.php',
    'JsonMachine\\JsonDecoder\\ValidResult' => $vendorDir . '/halaxa/json-machine/src/JsonDecoder/ValidResult.php',
    'JsonMachine\\Parser' => $vendorDir . '/halaxa/json-machine/src/Parser.php',
    'JsonMachine\\PositionAware' => $vendorDir . '/halaxa/json-machine/src/PositionAware.php',
    'JsonMachine\\RecursiveItems' => $vendorDir . '/halaxa/json-machine/src/RecursiveItems.php',
    'JsonMachine\\ResumableIteratorAggregateProxy' => $vendorDir . '/halaxa/json-machine/src/ResumableIteratorAggregateProxy.php',
    'JsonMachine\\StreamChunks' => $vendorDir . '/halaxa/json-machine/src/StreamChunks.php',
    'JsonMachine\\StringChunks' => $vendorDir . '/halaxa/json-machine/src/StringChunks.php',
    'JsonMachine\\Tokens' => $vendorDir . '/halaxa/json-machine/src/Tokens.php',
    'JsonMachine\\TokensWithDebugging' => $vendorDir . '/halaxa/json-machine/src/TokensWithDebugging.php',
    'JsonMachine\\ValidJsonPointers' => $vendorDir . '/halaxa/json-machine/src/ValidJsonPointers.php',
    'Sabberworm\\CSS\\CSSElement' => $vendorDir . '/sabberworm/php-css-parser/src/CSSElement.php',
    'Sabberworm\\CSS\\CSSList\\AtRuleBlockList' => $vendorDir . '/sabberworm/php-css-parser/src/CSSList/AtRuleBlockList.php',
    'Sabberworm\\CSS\\CSSList\\CSSBlockList' => $vendorDir . '/sabberworm/php-css-parser/src/CSSList/CSSBlockList.php',
    'Sabberworm\\CSS\\CSSList\\CSSList' => $vendorDir . '/sabberworm/php-css-parser/src/CSSList/CSSList.php',
    'Sabberworm\\CSS\\CSSList\\Document' => $vendorDir . '/sabberworm/php-css-parser/src/CSSList/Document.php',
    'Sabberworm\\CSS\\CSSList\\KeyFrame' => $vendorDir . '/sabberworm/php-css-parser/src/CSSList/KeyFrame.php',
    'Sabberworm\\CSS\\Comment\\Comment' => $vendorDir . '/sabberworm/php-css-parser/src/Comment/Comment.php',
    'Sabberworm\\CSS\\Comment\\Commentable' => $vendorDir . '/sabberworm/php-css-parser/src/Comment/Commentable.php',
    'Sabberworm\\CSS\\OutputFormat' => $vendorDir . '/sabberworm/php-css-parser/src/OutputFormat.php',
    'Sabberworm\\CSS\\OutputFormatter' => $vendorDir . '/sabberworm/php-css-parser/src/OutputFormatter.php',
    'Sabberworm\\CSS\\Parser' => $vendorDir . '/sabberworm/php-css-parser/src/Parser.php',
    'Sabberworm\\CSS\\Parsing\\Anchor' => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/Anchor.php',
    'Sabberworm\\CSS\\Parsing\\OutputException' => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/OutputException.php',
    'Sabberworm\\CSS\\Parsing\\ParserState' => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/ParserState.php',
    'Sabberworm\\CSS\\Parsing\\SourceException' => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/SourceException.php',
    'Sabberworm\\CSS\\Parsing\\UnexpectedEOFException' => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/UnexpectedEOFException.php',
    'Sabberworm\\CSS\\Parsing\\UnexpectedTokenException' => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/UnexpectedTokenException.php',
    'Sabberworm\\CSS\\Position\\Position' => $vendorDir . '/sabberworm/php-css-parser/src/Position/Position.php',
    'Sabberworm\\CSS\\Position\\Positionable' => $vendorDir . '/sabberworm/php-css-parser/src/Position/Positionable.php',
    'Sabberworm\\CSS\\Property\\AtRule' => $vendorDir . '/sabberworm/php-css-parser/src/Property/AtRule.php',
    'Sabberworm\\CSS\\Property\\CSSNamespace' => $vendorDir . '/sabberworm/php-css-parser/src/Property/CSSNamespace.php',
    'Sabberworm\\CSS\\Property\\Charset' => $vendorDir . '/sabberworm/php-css-parser/src/Property/Charset.php',
    'Sabberworm\\CSS\\Property\\Import' => $vendorDir . '/sabberworm/php-css-parser/src/Property/Import.php',
    'Sabberworm\\CSS\\Property\\KeyframeSelector' => $vendorDir . '/sabberworm/php-css-parser/src/Property/KeyframeSelector.php',
    'Sabberworm\\CSS\\Property\\Selector' => $vendorDir . '/sabberworm/php-css-parser/src/Property/Selector.php',
    'Sabberworm\\CSS\\Renderable' => $vendorDir . '/sabberworm/php-css-parser/src/Renderable.php',
    'Sabberworm\\CSS\\RuleSet\\AtRuleSet' => $vendorDir . '/sabberworm/php-css-parser/src/RuleSet/AtRuleSet.php',
    'Sabberworm\\CSS\\RuleSet\\DeclarationBlock' => $vendorDir . '/sabberworm/php-css-parser/src/RuleSet/DeclarationBlock.php',
    'Sabberworm\\CSS\\RuleSet\\RuleSet' => $vendorDir . '/sabberworm/php-css-parser/src/RuleSet/RuleSet.php',
    'Sabberworm\\CSS\\Rule\\Rule' => $vendorDir . '/sabberworm/php-css-parser/src/Rule/Rule.php',
    'Sabberworm\\CSS\\Settings' => $vendorDir . '/sabberworm/php-css-parser/src/Settings.php',
    'Sabberworm\\CSS\\Value\\CSSFunction' => $vendorDir . '/sabberworm/php-css-parser/src/Value/CSSFunction.php',
    'Sabberworm\\CSS\\Value\\CSSString' => $vendorDir . '/sabberworm/php-css-parser/src/Value/CSSString.php',
    'Sabberworm\\CSS\\Value\\CalcFunction' => $vendorDir . '/sabberworm/php-css-parser/src/Value/CalcFunction.php',
    'Sabberworm\\CSS\\Value\\CalcRuleValueList' => $vendorDir . '/sabberworm/php-css-parser/src/Value/CalcRuleValueList.php',
    'Sabberworm\\CSS\\Value\\Color' => $vendorDir . '/sabberworm/php-css-parser/src/Value/Color.php',
    'Sabberworm\\CSS\\Value\\LineName' => $vendorDir . '/sabberworm/php-css-parser/src/Value/LineName.php',
    'Sabberworm\\CSS\\Value\\PrimitiveValue' => $vendorDir . '/sabberworm/php-css-parser/src/Value/PrimitiveValue.php',
    'Sabberworm\\CSS\\Value\\RuleValueList' => $vendorDir . '/sabberworm/php-css-parser/src/Value/RuleValueList.php',
    'Sabberworm\\CSS\\Value\\Size' => $vendorDir . '/sabberworm/php-css-parser/src/Value/Size.php',
    'Sabberworm\\CSS\\Value\\URL' => $vendorDir . '/sabberworm/php-css-parser/src/Value/URL.php',
    'Sabberworm\\CSS\\Value\\Value' => $vendorDir . '/sabberworm/php-css-parser/src/Value/Value.php',
    'Sabberworm\\CSS\\Value\\ValueList' => $vendorDir . '/sabberworm/php-css-parser/src/Value/ValueList.php',
);

(()=>{const{domReady:e,$$:t}=window.blockartUtils;e((()=>{const e=function(){const i=Array.from(t(".blockart-timeline-wrapper ~ .blockart-timeline-line"));for(const e of i){const t=e?.previousElementSibling,i=t?.querySelectorAll(".blockart-timeline-marker"),l=i?.item(0),n=i?.item(i.length-1);if(!l||!n)return;const o=l.offsetTop+l.offsetHeight,s=n.offsetTop+n.offsetHeight,f=e?.offsetWidth,r=25+i?.item(0)?.offsetWidth/2-f/2,c=t.nextElementSibling;c.style.display="inline-block",c.style.right="unset",e.style.top=o+"px",e.style.height=s-o+"px",e.style.left=r+"px",i?.item(0)?.getBoundingClientRect()?.left&&t?.getBoundingClientRect()?.left&&(e.style.left=i?.item(0)?.getBoundingClientRect()?.left+i?.item(0)?.offsetWidth/2-f/2+"px")}let l;window.addEventListener("resize",(()=>{l&&clearTimeout(l),l=setTimeout((()=>{e()}),5)}))};e()}))})();
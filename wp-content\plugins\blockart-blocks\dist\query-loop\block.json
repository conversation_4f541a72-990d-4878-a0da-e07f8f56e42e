{"$schema": "https://schemas.wp.org/trunk/block.json", "name": "blockart/query-loop", "title": "Query Loop", "description": "Display posts listed according to various filters.", "keywords": ["query loop", "column", "layout"], "category": "blockart", "textdomain": "blockart", "supports": {"className": false, "customClassName": false, "align": ["center", "wide", "full"]}, "example": {"attributes": {}}, "attributes": {"clientId": {"type": "string"}, "queryId": {"type": "string"}, "query": {"type": "object", "default": {"post_type": "post", "per_page": 5, "pages": 0, "offset": 0, "order": "desc", "orderby": "date", "search": "", "status": "", "before": "", "after": "", "author": [], "author_exclude": [], "categories": [], "categories_exclude": [], "tags": [], "tags_exclude": [], "exclude": [], "sticky": "", "inherit": true, "taxQuery": null, "parents": []}}, "queryArgs": {"type": "array", "default": ["order"]}, "postType": {"type": "string", "default": "posts"}, "orderBy": {"type": "string", "default": "id"}, "order": {"type": "string", "default": "asc"}, "postLimit": {"type": "number", "default": 10}, "boxPadding": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true, "top": 10, "right": 5, "bottom": 10, "left": 5}}, "style": [{"selector": "{{WRAPPER}} .single-post {padding: {{VALUE}}; }"}]}, "alignment": {"type": "object", "style": [{"selector": "{{WRAPPER}} {text-align: {{VALUE}}; }"}]}, "titleTypography": {"type": "object", "default": {"typography": 1, "weight": 500, "size": {"desktop": {"value": 24}}}, "style": [{"selector": "{{WRAPPER}} .post-title"}]}, "dateTypography": {"type": "object", "default": {"typography": 1, "weight": 100, "size": {"desktop": {"value": 14}}}, "style": [{"selector": "{{WRAPPER}} .post-date"}]}, "descriptionTypography": {"type": "object", "default": {"typography": 1, "size": {"desktop": {"value": 16}}}, "style": [{"selector": "{{WRAPPER}} .post-excerpt"}]}, "hasModal": {"type": "boolean", "default": false}, "modalOnly": {"type": "boolean", "default": false}, "columns": {"type": "number", "default": ""}, "childRow": {"type": "boolean", "default": false}, "verticalAlignment": {"type": "object", "default": {"desktop": ""}, "style": [{"selector": "{{WRAPPER}} > [class*=\"blockart-container\"] > .blockart-query-loop-inner{ align-items: {{VALUE}}; }"}]}, "container": {"type": "string", "default": "contained"}, "inheritFromTheme": {"type": "boolean", "default": false}, "width": {"type": "object", "default": {"desktop": {"value": 1170, "unit": "px"}}, "style": [{"condition": [{"key": "container", "relation": "==", "value": "contained"}, {"key": "inheritFromTheme", "relation": "!=", "value": true}], "selector": "{{WRAPPER}} > .blockart-container { max-width: {{VALUE}}; }"}]}, "columnGap": {"type": "object", "default": {"desktop": {"value": 30, "unit": "px"}}, "style": [{"selector": "{{WRAPPER}} > [class*=\"blockart-container\"] > .blockart-query-loop-inner { --colSpacing: {{VALUE}}; margin-left: calc(var(--colSpacing) * -1) }"}]}, "rowGap": {"type": "object", "default": {"desktop": {"value": 30, "unit": "px"}}, "style": [{"selector": "{{WRAPPER}} > [class*=\"blockart-container\"] > .blockart-query-loop-inner { row-gap: {{VALUE}}; }"}]}, "height": {"type": "string", "default": "default"}, "minHeight": {"type": "object", "style": [{"condition": [{"key": "height", "relation": "==", "value": "min-height"}], "selector": "{{WRAPPER}} > [class*=\"blockart-container\"]  > .blockart-query-loop-inner {min-height: {{VALUE}};}"}]}, "background": {"type": "object", "default": {"background": 1}, "style": [{"selector": "{{WRAPPER}}.blockart-query-loop"}]}, "hoverBackground": {"type": "object", "default": {"background": 1}, "style": [{"selector": "{{WRAPPER}}:hover"}]}, "border": {"type": "object", "default": {"border": 1, "radius": {"desktop": {"lock": true}}, "size": {"desktop": {"lock": true}}}, "style": [{"selector": "{{WRAPPER}}"}]}, "borderHover": {"type": "object", "default": {"border": 1, "radius": {"desktop": {"lock": true}}, "size": {"desktop": {"lock": true}}}, "style": [{"selector": "{{WRAPPER}}:hover"}]}, "topSeparator": {"type": "object", "default": {"topSeparator": 1, "topSeparatorIcon": "top_separator_1"}, "style": [{"selector": "{{WRAPPER}} .blockart-top-separator svg"}]}, "bottomSeparator": {"type": "object", "default": {"bottomSeparator": 1, "bottomSeparatorIcon": "bottom_separator_1"}, "style": [{"selector": "{{WRAPPER}} .blockart-bottom-separator svg"}]}, "boxShadow": {"type": "object", "default": {"boxShadow": 1}, "style": [{"selector": "{{WRAPPER}}"}]}, "boxShadowHover": {"type": "object", "default": {"boxShadow": 1}, "style": [{"selector": "{{WRAPPER}}:hover"}]}, "overlay": {"type": "boolean", "default": false}, "overlayBackground": {"type": "object", "default": {"background": 1, "color": "rgba(37, 99, 235,0.3)"}, "style": [{"condition": [{"key": "overlay", "relation": "==", "value": true}], "selector": "{{WRAPPER}} > .blockart-overlay"}]}, "blockMargin": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { margin: {{VALUE}}; }"}]}, "blockPadding": {"type": "object", "default": {"dimension": 1, "desktop": {"left": 15, "right": 15, "unit": "px"}}, "style": [{"selector": "{{WRAPPER}} { padding: {{VALUE}}; }"}]}, "blockZIndex": {"type": "number", "style": [{"selector": "{{WRAPPER}} { z-index: {{VALUE}}; }"}]}, "cssID": {"type": "string"}, "animation": {"type": "string"}, "interaction": {"type": "object"}, "position": {"type": "object"}, "hideOnDesktop": {"type": "boolean", "style": [{"selector": "@media (min-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnTablet": {"type": "boolean", "style": [{"selector": "@media (min-width:48em) and (max-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} { display: none; } }"}]}, "colReverseOnTablet": {"type": "boolean", "style": [{"selector": "@media (max-width:62em) { {{WRAPPER}} > .blockart-container > .blockart-query-loop-inner { flex-direction:column-reverse; } }"}]}, "colReverseOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} > .blockart-container > .blockart-query-loop-inner { flex-direction:column-reverse; } }"}]}, "blockCSS": {"type": "string"}, "className": {"type": "string"}}, "providesContext": {"blockart/queryId": "queryId", "query": "query", "queryArgs": "queryArgs", "displayLayout": "displayLayout", "enhancedPagination": "enhancedPagination"}, "style": "blockart-blocks", "editorScript": "blockart-blocks", "editorStyle": "blockart-blocks-editor"}
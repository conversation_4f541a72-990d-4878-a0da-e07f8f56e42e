/**
 * demo-importer.scss
 * General ThemeGrill Demo Importer admin styles.
 */

/**
 * Import.
 */
@import 'animation';

/**
 * Styling begins.
 */
.appearance_page_demo-importer {
	.demo-importer {
		.wp-filter {
			padding: 0 20px;

			.search-form {
				margin-left: 1em;
			}

			.filter-section {
				float: left;

				&.right {
					float: right;
				}
			}

			.filter-links li > a:focus {
				box-shadow: none;
			}
		}

		.theme-browser {
			.theme {
				.premium-demo-banner {
					position: absolute;
					top: 15px;
					right: -16px;
					color: #fff;
					padding: 0 20px;
					font-weight: 600;
					min-height: 30px;
					line-height: 28px;
					background: #5fbd0a;
					text-transform: uppercase;

					&::before,
					&::after {
						content: '';
						position: absolute;
					}

					&::before {
						left: -15px;
						color: #fff;
						border: 15px solid #5fbd0a;
						border-left-color: transparent;
					}

					&::after {
						top: 30px;
						right: 0;
						border: 8px solid #478d07;
						border-right-color: transparent;
						border-bottom-color: transparent;
					}
				}

				.theme-screenshot img {
					object-fit: cover;
				}
			}
		}

		.theme-install-overlay {
			.wp-full-overlay-header {
				background-color: #eee;
			}

			.wp-full-overlay-sidebar-content {
				z-index: 1;
				bottom: 132px;

				.install-theme-info {
					.theme-version {
						margin-top: 0;
					}

					.theme-screenshot {
						width: 100%;
						box-sizing: border-box;
					}

					.premium-demo-tag {
						color: #fff;
						font-size: 13px;
						font-weight: 400;
						margin-left: 5px;
						padding: 0 10px;
						line-height: 22px;
						border-radius: 3px;
						background: #67c016;
						display: inline-block;
					}

					table.plugins-list-table {
						margin-bottom: 1em;

						.plugin-status {
							float: right;

							span {
								width: 20px;
								height: 20px;
								overflow: hidden;
								border-radius: 50%;
								position: relative;
								vertical-align: top;
								white-space: nowrap;
								text-indent: -9999px;
								display: inline-block;
								border: 2px solid #bfbfbf;

								&.active {
									border-color: #5cb85c;

									&::after {
										position: absolute;
										left: 50%;
										top: 50%;
										opacity: 1;
										width: 25%;
										height: 50%;
										content: '';
										transform-origin: left top;
										border-top: 2px solid #5cb85c;
										border-right: 2px solid #5cb85c;
										transform: scaleX(-1) rotate(135deg) translate(-58%, -40%);
									}
								}

								&.updating-message {
									border-left-color: #5cb85c;
									animation: spin 0.75s linear infinite;
								}
							}
						}
					}
				}
			}

			.wp-full-overlay-footer {
				height: 132px;

				.devices button {
					&:hover,
					&:focus {
						background-color: transparent;
					}
				}

				.demo-import-actions {
					padding: 20px;
					background: #fff;
					border-bottom: 1px solid #ddd;

					.button {
						width: 100%;
						text-align: center;

						&.updated-message,
						&.updating-message {
							&::before {
								margin-top: 11px;
							}
						}
					}
				}
			}

			&.expanded {
				.wp-full-overlay-footer {
					left: initial;
				}
			}
		}
	}
}

/**
 * Tooltips
 */
.tips {
	cursor: help;
	text-decoration: none;
}

img.tips {
	padding: 5px 0 0;
}

#tiptip_holder {
	display: none;
	z-index: 8675309;
	position: absolute;
	top: 0;
	/*rtl:ignore*/
	left: 0;

	&.tip_top {
		padding-bottom: 5px;

		#tiptip_arrow_inner {
			margin-top: -7px;
			margin-left: -6px;
			border-top-color: #333;
		}
	}

	&.tip_bottom {
		padding-top: 5px;

		#tiptip_arrow_inner {
			margin-top: -5px;
			margin-left: -6px;
			border-bottom-color: #333;
		}
	}

	&.tip_right {
		padding-left: 5px;

		#tiptip_arrow_inner {
			margin-top: -6px;
			margin-left: -5px;
			border-right-color: #333;
		}
	}

	&.tip_left {
		padding-right: 5px;

		#tiptip_arrow_inner {
			margin-top: -6px;
			margin-left: -7px;
			border-left-color: #333;
		}
	}
}

#tiptip_content {
	color: #fff;
	font-size: 0.8em;
	max-width: 150px;
	background: #333;
	text-align: center;
	border-radius: 3px;
	padding: 0.618em 1em;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);

	code {
		padding: 1px;
		background: #888;
	}
}

#tiptip_arrow,
#tiptip_arrow_inner {
	position: absolute;
	border-color: transparent;
	border-style: solid;
	border-width: 6px;
	height: 0;
	width: 0;
}

/*rtl:raw:
#tiptip_arrow {
	right: 50%;
	margin-right: -6px;
}
*/

/**
 * Demo Import message CSS.
 */
.demo-import-confirm-message {
	font-size: 14px;
	padding: 10px 10px 0;

	ol {
		margin-left: 20px;

		.warning {
			color: #2563eb;
		}
	}
}

.jconfirm-buttons {

	.demo-import-confirm-button {
		background: #f00000;
		color: #fff;

		&.disabled {
			pointer-events: none;
			opacity: 0.3;
		}
	}

	.demo-import-cancel-button {
		background: #eee;
		border-color: #999;
		color: #23282d;
	}
}

body {

	&.demo-import-message-popup {
		overflow: visible;
		visibility: visible;
	}
}


.demo-import-notice-warning {
	color: #f00000c7;
	padding: 16px 30px;
	border: 1px solid #f0000029;
	border-radius: 6px;
	background: #f0000008;
	position: relative;

	p {
		margin: 0;
	}

	span {
		color: #000000;
	}

	a {
		text-decoration: underline;
	}

	&::before {
		content: '\f534';
		font-family: 'dashicons';
		position: absolute;
		top: 50%;
		left: 10px;
		transform: translateY(-50%);
		color: #f00000;
	}
}

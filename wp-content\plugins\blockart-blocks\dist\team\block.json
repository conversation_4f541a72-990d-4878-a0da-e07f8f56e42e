{"$schema": "https://schemas.wp.org/trunk/block.json", "name": "blockart/team", "title": "Team", "description": "Create team layout with heading, subheading, description and image.", "keywords": ["team"], "category": "blockart", "textdomain": "blockart", "supports": {"className": false, "customClassName": false}, "example": {"attributes": {}}, "attributes": {"clientId": {"type": "string"}, "markup": {"type": "string", "default": "h2"}, "teamName": {"type": "string", "default": "<PERSON>"}, "teamDesignation": {"type": "string", "default": "Designation"}, "link": {"type": "object"}, "buttonEnable": {"type": "boolean", "default": false}, "buttonIcon": {"type": "string", "default": "chevronRight"}, "buttonPadding": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} .blockart-team-introduction .blockart-intro-wrapper.blockart-hover-effect .blockart-button{ padding: {{VALUE}}; }"}]}, "buttonColor": {"type": "string", "style": [{"selector": "{{WRAPPER}} .blockart-team-introduction .blockart-intro-wrapper.blockart-hover-effect .blockart-button svg { fill: {{VALUE}} }"}]}, "hoverButtonColor": {"type": "string", "style": [{"selector": "{{WRAPPER}} .blockart-team-introduction .blockart-intro-wrapper.blockart-hover-effect .blockart-button:hover svg { fill: {{VALUE}} }"}]}, "buttonBackground": {"type": "object", "default": {"background": 1}, "style": [{"selector": "{{WRAPPER}} .blockart-team-introduction .blockart-intro-wrapper.blockart-hover-effect .blockart-button"}]}, "hoverButtonBackground": {"type": "object", "default": {"background": 1}, "style": [{"selector": "{{WRAPPER}} .blockart-team-introduction .blockart-intro-wrapper.blockart-hover-effect .blockart-button:hover"}]}, "image": {"type": "object", "default": {}}, "imageEnable": {"type": "boolean", "default": true}, "imageWidth": {"type": "string", "style": [{"selector": "{{WRAPPER}} .blockart-team-image .blockart-image { width: {{VALUE}}; height: auto; }"}]}, "teamContent": {"type": "string", "default": "Click here to change this text. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo."}, "background": {"type": "object", "default": {"background": 1}, "style": [{"selector": "{{WRAPPER}}"}]}, "alignment": {"type": "object", "style": [{"selector": "{{WRAPPER}} .blockart-team-container {text-align: {{VALUE}}; }"}]}, "headingTypography": {"type": "object", "default": {"typography": 1, "weight": 400}, "style": [{"selector": "{{WRAPPER}} .blockart-team-name .blockart-team-member-name"}]}, "designationTypography": {"type": "object", "default": {"typography": 1, "weight": 400}, "style": [{"selector": "{{WRAPPER}} .blockart-team-designation p"}]}, "textTypography": {"type": "object", "default": {"typography": 1, "weight": 400}, "style": [{"selector": "{{WRAPPER}} .blockart-team-content p"}]}, "border": {"type": "object", "default": {"border": 1, "radius": {"desktop": {"lock": true}}, "size": {"desktop": {"lock": true}}}, "style": [{"selector": "{{WRAPPER}}"}]}, "headingMargin": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} .blockart-team-name .blockart-team-member-name { margin: {{VALUE}}; }"}]}, "designationMargin": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} .blockart-team-designation p { margin: {{VALUE}}; }"}]}, "textMargin": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} .blockart-team-content p { margin: {{VALUE}}; }"}]}, "headingColor": {"type": "string", "style": [{"selector": "{{WRAPPER}} .blockart-team-name .blockart-team-member-name { color: {{VALUE}} }"}]}, "headingHoverColor": {"type": "string", "style": [{"selector": "{{WRAPPER}} .blockart-team-introduction:hover .blockart-team-name .blockart-team-member-name { color: {{VALUE}} }"}]}, "designationColor": {"type": "string", "style": [{"selector": "{{WRAPPER}} .blockart-team-designation p { color: {{VALUE}} }"}]}, "designationHoverColor": {"type": "string", "style": [{"selector": "{{WRAPPER}} .blockart-team-introduction:hover .blockart-team-designation p { color: {{VALUE}} }"}]}, "textColor": {"type": "string", "style": [{"selector": "{{WRAPPER}} .blockart-team-content p { color: {{VALUE}} }"}]}, "headingEnable": {"type": "boolean", "default": true}, "designationEnable": {"type": "boolean", "default": true}, "textEnable": {"type": "boolean", "default": true}, "blockMargin": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { margin: {{VALUE}}; }"}]}, "blockPadding": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { padding: {{VALUE}}; }"}]}, "blockZIndex": {"type": "number", "style": [{"selector": "{{WRAPPER}} { z-index: {{VALUE}}; }"}]}, "cssID": {"type": "string"}, "animation": {"type": "string"}, "interaction": {"type": "object"}, "position": {"type": "object"}, "hideOnDesktop": {"type": "boolean", "style": [{"selector": "@media (min-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnTablet": {"type": "boolean", "style": [{"selector": "@media (min-width:48em) and (max-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} { display: none; } }"}]}, "colReverseOnTablet": {"type": "boolean", "style": [{"selector": "@media (max-width:62em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "colReverseOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "blockCSS": {"type": "string"}, "className": {"type": "string"}, "icons": {"type": "object", "default": {"icon1": {"enable": true, "icon": "facebook", "link": "#"}, "icon2": {"enable": true, "icon": "linkedin", "link": "#"}, "icon3": {"enable": true, "icon": "twitter", "link": "#"}, "icon4": {"enable": true, "icon": "pinterest", "link": "#"}, "icon5": {"type": "object", "default": {"enable": true, "icon": "youtube", "link": "#"}}}}, "iconSize": {"type": "object", "style": [{"selector": "{{WRAPPER}} .blockart-team-icon .blockart-icon { width: {{VALUE}}; height: auto; }"}]}}, "style": "blockart-blocks", "editorScript": "blockart-blocks", "editorStyle": "blockart-blocks-editor"}
"use strict";let defaultOptions={container:null,filters:null,hideTexts:null,textPosition:"top",linesOrientation:"horizontal",rotate:0,arrayBackgroundColorText:null,arrayColorText:null,linesColor:null},Dics=function(t){this.options=utils.extend({},[defaultOptions,t],{clearEmpty:!0}),this.container=this.options.container,null==this.container?console.error("Container element not found!"):(this._setOrientation(this.options.linesOrientation,this.container),this.images=this._getImages(),this.sliders=[],this._activeSlider=0,this._load(this.images[0]))};Dics.prototype._load=function(t,i=1e5){t.naturalWidth?(this._buidAfterFirstImageLoad(t),window.addEventListener("resize",()=>{this._setContainerWidth(t),this._resetSizes()})):i>0?(i--,setTimeout(()=>{this._load(t,i)},100)):console.error("error loading images")},Dics.prototype._buidAfterFirstImageLoad=function(t){this._setContainerWidth(t),this._build(),this._setEvents()},Dics.prototype._setContainerWidth=function(t){this.options.container.style.height=`${this._calcContainerHeight(t)}px`},Dics.prototype._setOpacityContainerForLoading=function(t){this.options.container.style.opacity=t},Dics.prototype._resetSizes=function(){let t=this.images.length,i=this.container.getBoundingClientRect()[this.config.sizeField]/t,e=this.container.querySelectorAll("[data-function='b-dics__section']");for(let s=0;s<e.length;s++){let o=e[s];o.style.flex=`0 0 ${i}px`,o.querySelector(".b-dics__image").style[this.config.positionField]=`${-(s*i)}px`;let n=o.querySelector(".b-dics__slider");n&&(n.style[this.config.positionField]=`${i*(s+1)}px`)}},Dics.prototype._build=function(){this._applyGlobalClass(this.options);let t=this.images.length,i=this.container.getBoundingClientRect()[this.config.sizeField]/t;for(let e=0;e<t;e++){let s=this.images[e],o=this._createElement("div","b-dics__section"),n=this._createElement("div","b-dics__image-container"),l=this._createSlider(e,i);this._createAltText(s,e,n),this._applyFilter(s,e,this.options.filters),this._rotate(s,n),o.setAttribute("data-function","b-dics__section"),o.style.flex=`0 0 ${i}px`,s.classList.add("b-dics__image"),o.appendChild(n),n.appendChild(s),e<t-1&&o.appendChild(l),this.container.appendChild(o),s.style[this.config.positionField]=`${-(e*i)}px`}this.sections=this._getSections(),this._setOpacityContainerForLoading(1)},Dics.prototype._getImages=function(){return this.container.querySelectorAll("img")},Dics.prototype._getSections=function(){return this.container.querySelectorAll('[data-function="b-dics__section"]')},Dics.prototype._createElement=function(t,i){let e=document.createElement(t);return e.classList.add(i),e},Dics.prototype._setEvents=function(){let t=this;t._disableImageDrag(),t._isGoingRight=null;let i=0,e=function(e){let s=e.pageX?e.pageX:e.touches[0].pageX;s<i?t._isGoingRight=!1:s>i&&(t._isGoingRight=!0),i=s;let o=t._calcPosition(e),n=t._beforeSectionsWidth(t.sections,t.images,t._activeSlider);t.sliders[t._activeSlider].style[t.config.positionField]=`${o}px`,t._pushSections(o-n,o)};t.container.addEventListener("click",e);for(let s=0;s<t.sliders.length;s++){let o=t.sliders[s];utils.setMultiEvents(o,["mousedown","touchstart"],function(i){t._activeSlider=s,t._clickPosition=t._calcPosition(i),o.classList.add("b-dics__slider--active"),utils.setMultiEvents(t.container,["mousemove","touchmove"],e)})}let n=function(){let i=t.container.querySelectorAll(".b-dics__slider--active");for(let s of i)s.classList.remove("b-dics__slider--active"),utils.removeMultiEvents(t.container,["mousemove","touchmove"],e)};utils.setMultiEvents(document.body,["mouseup","touchend"],n)},Dics.prototype._beforeSectionsWidth=function(t,i,e){let s=0;for(let o=0;o<t.length;o++){let n=t[o];if(o===e)return s;s+=n.getBoundingClientRect()[this.config.sizeField]}},Dics.prototype._calcContainerHeight=function(t){let i=t.naturalHeight,e=t.naturalWidth;return this.options.container.getBoundingClientRect().width/e*i},Dics.prototype._setLeftToImages=function(t,i){let e=0;for(let s=0;s<i.length;s++)i[s].style[this.config.positionField]=`-${e}px`,e+=t[s].getBoundingClientRect()[this.config.sizeField],this.sliders[s].style[this.config.positionField]=`${e}px`},Dics.prototype._disableImageDrag=function(){for(let t=0;t<this.images.length;t++)this.sliders[t].addEventListener("dragstart",function(t){t.preventDefault()}),this.images[t].addEventListener("dragstart",function(t){t.preventDefault()})},Dics.prototype._applyFilter=function(t,i,e){e&&(t.style.filter=e[i])},Dics.prototype._applyGlobalClass=function(t){let i=t.container;t.hideTexts&&i.classList.add("b-dics--hide-texts"),"vertical"===t.linesOrientation&&i.classList.add("b-dics--vertical"),"center"===t.textPosition?i.classList.add("b-dics--tp-center"):"bottom"===t.textPosition?i.classList.add("b-dics--tp-bottom"):"bottom-left"===t.textPosition?i.classList.add("b-dics--tp-bottom-left"):"left"===t.textPosition?i.classList.add("b-dics--tp-left"):"right"===t.textPosition&&i.classList.add("b-dics--tp-right")},Dics.prototype._createSlider=function(t,i){let e=this._createElement("div","b-dics__slider"),s=`
    <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_5330_2718)">
    <path d="M16.622 9.42186L16.5593 18.6262L22.6907 14.0656L16.622 9.42186Z" fill="white"/>
    </g>
    <g clip-path="url(#clip1_5330_2718)">
    <path d="M11.4594 18.6596L11.5222 9.45525L5.39074 14.0159L11.4594 18.6596Z" fill="white"/>
    </g>
    <circle cx="14" cy="14" r="12.8333" stroke="white" stroke-width="2.33333"/>
    <defs>
    <clipPath id="clip0_5330_2718">
    <rect width="10.0475" height="7.06798" fill="white" transform="translate(23.0681 9.00047) rotate(90.3904)"/>
    </clipPath>
    <clipPath id="clip1_5330_2718">
    <rect width="10.0475" height="7.06798" fill="white" transform="translate(4.93185 18.9995) rotate(-89.6096)"/>
    </clipPath>
    </defs>
    </svg>
    `;return e.innerHTML=s,this.options.linesColor&&(e.style.color=this.options.linesColor),e.style[this.config.positionField]=`${i*(t+1)}px`,this.sliders.push(e),e},Dics.prototype._createAltText=function(t,i,e){let s=t.getAttribute("alt");if(s){let o=this._createElement("p","b-dics__text");this.options.arrayBackgroundColorText&&(o.style.backgroundColor=this.options.arrayBackgroundColorText[i]),this.options.arrayColorText&&(o.style.color=this.options.arrayColorText[i]),o.appendChild(document.createTextNode(s)),e.appendChild(o)}},Dics.prototype._rotate=function(t,i){t.style.rotate=`-${this.options.rotate}`,i.style.rotate=this.options.rotate},Dics.prototype._removeActiveElements=function(){let t=Dics.container.querySelectorAll(".b-dics__slider--active");for(let i of t)i.classList.remove("b-dics__slider--active"),utils.removeMultiEvents(Dics.container,["mousemove","touchmove"],Dics.prototype._removeActiveElements)},Dics.prototype._setOrientation=function(t){this.config={},"vertical"===t?(this.config.offsetSizeField="offsetHeight",this.config.offsetPositionField="offsetTop",this.config.sizeField="height",this.config.positionField="top",this.config.clientField="clientY",this.config.pageField="pageY"):(this.config.offsetSizeField="offsetWidth",this.config.offsetPositionField="offsetLeft",this.config.sizeField="width",this.config.positionField="left",this.config.clientField="clientX",this.config.pageField="pageX")},Dics.prototype._calcPosition=function(t){let i=this.container.getBoundingClientRect(),e=isNaN(t[this.config.clientField])?t.touches[0][this.config.clientField]:t[this.config.clientField];return i[this.config.positionField]<e?e-i[this.config.positionField]:0},Dics.prototype._pushSections=function(t,i){this._setFlex(i,this._isGoingRight);let e=this.sections[this._activeSlider],s=this.sections[this._activeSlider+1],o=s.getBoundingClientRect()[this.config.sizeField]-(t-this.sections[this._activeSlider].getBoundingClientRect()[this.config.sizeField]);e.style.flex=!0===this._isGoingRight?`2 0 ${t}px`:`1 1 ${t}px`,s.style.flex=!0===this._isGoingRight?` ${o}px`:`2 0 ${o}px`,this._setLeftToImages(this.sections,this.images)},Dics.prototype._setFlex=function(t,i){let e=0;for(let s=0;s<this.sections.length;s++){let o=this.sections[s],n=o.getBoundingClientRect()[this.config.sizeField];e+=n,i&&t>e-n&&s>this._activeSlider||!i&&t<e&&s<this._activeSlider?o.style.flex=`1 100 ${n}px`:o.style.flex=`0 0 ${n}px`}};let utils={extend:function(t,i,e){for(let s in i)i.hasOwnProperty(s)&&o(t,i[s]);function o(t,i){for(let s in i)if(i.hasOwnProperty(s)){let n=i[s];if("Object"===utils.getConstructor(n))t[s]||(t[s]={}),o(t[s],n);else{if(e.clearEmpty&&null==n)continue;t[s]=n}}}return t},setMultiEvents:function(t,i,e){for(let s=0;s<i.length;s++)t.addEventListener(i[s],e)},removeMultiEvents:function(t,i,e){for(let s=0;s<i.length;s++)t.removeEventListener(i[s],e,!1)},getConstructor:function(t){return Object.prototype.toString.call(t).slice(8,-1)}};
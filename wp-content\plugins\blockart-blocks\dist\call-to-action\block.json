{"$schema": "https://schemas.wp.org/trunk/block.json", "name": "blockart/call-to-action", "title": "Call to Action", "description": "Create desired action on your site", "keywords": ["call to action", "cta"], "category": "blockart", "textdomain": "blockart", "supports": {"className": false, "customClassName": false}, "example": {"attributes": {}}, "attributes": {"clientId": {"type": "string"}, "layout": {"type": "string", "default": "layout-1"}, "titleEnable": {"type": "boolean", "default": true}, "titleColor": {"type": "string", "style": [{"selector": "{{WRAPPER}} .blockart-title { color: {{VALUE}} }"}]}, "titleTypography": {"type": "object", "default": {"typography": 1}, "style": [{"selector": "{{WRAPPER}} .blockart-title"}]}, "textEnable": {"type": "boolean", "default": true}, "buttonEnable": {"type": "boolean", "default": true}, "textColor": {"type": "string", "style": [{"selector": "{{WRAPPER}} .blockart-text { color: {{VALUE}} }"}]}, "textTypography": {"type": "object", "default": {"typography": 1}, "style": [{"selector": "{{WRAPPER}} .blockart-text"}]}, "markup": {"type": "string", "default": "h2"}, "link": {"type": "object"}, "title": {"type": "string", "default": "Call to Action"}, "textButton": {"type": "string", "default": "<PERSON><PERSON>"}, "text": {"type": "string", "default": "Description for this block. Use this space to describing your block. Any text will do. Description to this block. You can use this space for describing your block."}, "buttonColor": {"type": "string", "default": "#FFFFFF", "style": [{"selector": "{{WRAPPER}} .blockart-button-wrapper .blockart-button {color: {{VALUE}}; }"}]}, "buttonBackground": {"type": "object", "default": {"background": 1, "type": "color", "color": "#2563eb"}, "style": [{"selector": "{{WRAPPER}} .blockart-button-wrapper .blockart-button"}]}, "buttonTypography": {"type": "object", "default": {"typography": 1, "weight": 400}, "style": [{"selector": "{{WRAPPER}} .blockart-button-wrapper .blockart-button"}]}, "buttonBorderEnable": {"type": "boolean", "default": true}, "buttonBorder": {"type": "object", "default": {"border": 1, "radius": {"desktop": {"lock": true}}, "size": {"desktop": {"lock": true}}}, "style": [{"selector": "{{WRAPPER}} .blockart-button-wrapper .blockart-button"}]}, "background": {"type": "object", "default": {"background": 1}, "style": [{"selector": "{{WRAPPER}}.blockart-call-to-action"}]}, "alignment": {"type": "object", "style": [{"selector": "{{WRAPPER}} {text-align: {{VALUE}}; } {{WRAPPER}}.blockart-call-to-action .blockart-content-wrapper {text-align: {{VALUE}}; }"}]}, "size": {"type": "string"}, "border": {"type": "object", "default": {"border": 1, "radius": {"desktop": {"lock": true}}, "size": {"desktop": {"lock": true}}}, "style": [{"selector": "{{WRAPPER}}"}]}, "hoverBorder": {"type": "object", "default": {"border": 1, "radius": {"desktop": {"lock": true}}, "size": {"desktop": {"lock": true}}}, "style": [{"selector": "{{WRAPPER}}:hover"}]}, "boxShadow": {"type": "object", "default": {"boxShadow": 1}, "style": [{"selector": "{{WRAPPER}}"}]}, "boxShadowHover": {"type": "object", "default": {"boxShadow": 1}, "style": [{"selector": "{{WRAPPER}}:hover"}]}, "blockMargin": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { margin: {{VALUE}}; }"}]}, "blockPadding": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}}.blockart-call-to-action { padding: {{VALUE}}; }"}]}, "blockZIndex": {"type": "number", "style": [{"selector": "{{WRAPPER}} { z-index: {{VALUE}}; }"}]}, "cssID": {"type": "string"}, "animation": {"type": "string"}, "interaction": {"type": "object"}, "position": {"type": "object"}, "hideOnDesktop": {"type": "boolean", "style": [{"selector": "@media (min-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnTablet": {"type": "boolean", "style": [{"selector": "@media (min-width:48em) and (max-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} { display: none; } }"}]}, "colReverseOnTablet": {"type": "boolean", "style": [{"selector": "@media (max-width:62em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "colReverseOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "blockCSS": {"type": "string"}, "className": {"type": "string"}, "highlightColor": {"type": "string", "default": "#fff", "style": [{"selector": ".blockart-heading{{WRAPPER}} > .blockart-highlight { color: {{VALUE}} }"}]}, "icon": {"type": "object", "default": {"enable": false, "icon": ""}}, "iconPosition": {"type": "string", "default": "right"}, "iconSize": {"type": "object", "style": [{"condition": [{"key": "icon", "relation": "!=", "value": ""}], "selector": "{{WRAPPER}} .blockart-button-icon .blockart-icon { width: {{VALUE}}; height: auto; }"}]}, "iconGap": {"type": "object", "style": [{"condition": [{"key": "icon", "relation": "!=", "value": ""}, {"key": "iconPosition", "relation": "==", "value": "left"}], "selector": "{{WRAPPER}} .blockart-button-icon { margin-right: {{VALUE}}; }"}, {"condition": [{"key": "icon", "relation": "!=", "value": ""}, {"key": "iconPosition", "relation": "==", "value": "right"}], "selector": "{{WRAPPER}} .blockart-button-icon { margin-left: {{VALUE}}; }"}]}}, "style": "blockart-blocks", "editorScript": "blockart-blocks", "editorStyle": "blockart-blocks-editor"}
{"$schema": "https://schemas.wp.org/trunk/block.json", "name": "blockart/post-template", "title": "Post Template", "description": "Post template block to display post list.", "keywords": ["post template", "text"], "category": "blockart", "textdomain": "blockart", "usesContext": ["blockart/queryId", "query", "queryArgs", "queryContext", "displayLayout", "templateSlug", "previewPostType", "enhancedPagination"], "supports": {"className": false, "customClassName": false}, "example": {"attributes": {}}, "attributes": {"clientId": {"type": "string"}, "typography": {"type": "object", "default": {"typography": 1}, "style": [{"selector": ".blockart-post-template{{WRAPPER}}"}]}, "columns": {"type": "number", "default": 1}, "columnGap": {"type": "object", "default": {"desktop": {"value": 30, "unit": "px"}}, "style": [{"selector": "{{WRAPPER}} {column-gap: {{VALUE}};}"}]}, "rowGap": {"type": "object", "default": {"desktop": {"value": 30, "unit": "px"}}, "style": [{"selector": "{{WRAPPER}} {row-gap: {{VALUE}};}"}]}, "textColor": {"type": "string", "style": [{"selector": "{{WRAPPER}} {color: {{VALUE}}; }"}]}, "textHoverColor": {"type": "string", "style": [{"selector": "{{WRAPPER}}:hover {color: {{VALUE}}; }"}]}, "background": {"type": "object", "default": {"background": 1}, "style": [{"selector": ".blockart-post-template{{WRAPPER}}"}]}, "hoverBackground": {"type": "object", "default": {"background": 1}, "style": [{"selector": ".blockart-post-template{{WRAPPER}}:hover"}]}, "border": {"type": "object", "default": {"border": 1, "radius": {"desktop": {"lock": true}}, "size": {"desktop": {"lock": true}}}, "style": [{"selector": "{{WRAPPER}}"}]}, "borderHover": {"type": "object", "default": {"border": 1, "radius": {"desktop": {"lock": true}}, "size": {"desktop": {"lock": true}}}, "style": [{"selector": "{{WRAPPER}}:hover"}]}, "margin": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": ".blockart-post-template{{WRAPPER}} { margin: {{VALUE}}; }"}]}, "blockMargin": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { margin: {{VALUE}}; }"}]}, "blockPadding": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { padding: {{VALUE}}; }"}]}, "blockZIndex": {"type": "number", "style": [{"selector": "{{WRAPPER}} { z-index: {{VALUE}}; }"}]}, "cssID": {"type": "string"}, "animation": {"type": "string"}, "interaction": {"type": "object"}, "position": {"type": "object"}, "hideOnDesktop": {"type": "boolean", "style": [{"selector": "@media (min-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnTablet": {"type": "boolean", "style": [{"selector": "@media (min-width:48em) and (max-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} { display: none; } }"}]}, "colReverseOnTablet": {"type": "boolean", "style": [{"selector": "@media (max-width:62em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "colReverseOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "blockCSS": {"type": "string"}, "className": {"type": "string"}}, "style": "blockart-blocks", "editorScript": "blockart-blocks", "editorStyle": "blockart-blocks-editor"}
{"$schema": "https://schemas.wp.org/trunk/block.json", "name": "blockart/price", "title": "Price", "keywords": ["price list", "price"], "category": "blockart", "textdomain": "blockart", "supports": {"className": false, "customClassName": false}, "parent": ["blockart/price-list"], "example": {"attributes": {}}, "attributes": {"clientId": {"type": "string"}, "text": {"type": "string"}, "color": {"type": "string", "style": [{"selector": "{{WRAPPER}} {color: {{VALUE}}; } {{WRAPPER}} .blockart-price {color: {{VALUE}}; }"}]}, "hoverColor": {"type": "string", "style": [{"selector": "{{WRAPPER}}:hover {color: {{VALUE}}; } {{WRAPPER}} .blockart-price:hover {color: {{VALUE}}; }"}]}, "blockMargin": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { margin: {{VALUE}}; }"}]}, "blockPadding": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { padding: {{VALUE}}; }"}]}, "blockZIndex": {"type": "number", "style": [{"selector": "{{WRAPPER}} { z-index: {{VALUE}}; }"}]}, "cssID": {"type": "string"}, "animation": {"type": "string"}, "interaction": {"type": "object"}, "position": {"type": "object"}, "hideOnDesktop": {"type": "boolean", "style": [{"selector": "@media (min-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnTablet": {"type": "boolean", "style": [{"selector": "@media (min-width:48em) and (max-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} { display: none; } }"}]}, "colReverseOnTablet": {"type": "boolean", "style": [{"selector": "@media (max-width:62em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "colReverseOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "blockCSS": {"type": "string"}, "className": {"type": "string"}, "buttonAlignment": {"type": "string", "default": "right", "style": [{"selector": "{{WRAPPER}} {text-align: {{VALUE}}; }"}]}, "vAlignment": {"type": "string", "default": "center", "style": [{"selector": "{{WRAPPER}} {align-items: {{VALUE}}; }"}]}, "priceUnit": {"type": "string", "default": "$"}, "price": {"type": "string", "default": "149"}, "priceSuffix": {"type": "string", "default": ".5"}, "timeSpan": {"type": "string", "default": "/year"}, "typography": {"type": "object", "default": {"typography": 1}, "style": [{"selector": "{{WRAPPER}}, {{WRAPPER}} .blockart-price"}]}, "priceMarkup": {"type": "string", "default": "h3"}, "decimalMarkup": {"type": "string", "default": "span"}, "spanMarkup": {"type": "string", "default": "span"}, "unitMarkup": {"type": "string", "default": "span"}}, "style": "blockart-blocks", "editorScript": "blockart-blocks", "editorStyle": "blockart-blocks-editor"}
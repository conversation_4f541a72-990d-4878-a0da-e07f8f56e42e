(()=>{var e={};e.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),(()=>{var t;e.g.importScripts&&(t=e.g.location+"");var r=e.g.document;if(!t&&r&&(r.currentScript&&"SCRIPT"===r.currentScript.tagName.toUpperCase()&&(t=r.currentScript.src),!t)){var i=r.getElementsByTagName("script");if(i.length)for(var n=i.length-1;n>-1&&(!t||!/^http(s?):/.test(t));)t=i[n--].src}if(!t)throw new Error("Automatic publicPath is not supported in this browser");t=t.replace(/^blob:/,"").replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),e.p=t})(),e.p=window._BLOCKART_WEBPACK_PUBLIC_PATH__,(()=>{const{$:e,$$:t,domReady:r,each:i,toArray:n,find:o}=window.blockartUtils;r((()=>{const e=n(t(".blockart-carousel")),r=n(t(".blockart-thumbnail-carousel"));e&&r&&(r&&i(r,(e=>{var t,r,i,n,a;const l=JSON.parse(null!==(t=e.dataset?.swiper)&&void 0!==t?t:"{}"),s=new window.Swiper(o(e,".thumbnail-swiper"),{spaceBetween:null!==(r=l?.spaceBetween)&&void 0!==r?r:10,slidesPerView:null!==(i=l?.slidesPerView)&&void 0!==i?i:4,freeMode:!0,watchSlidesProgress:!1,navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"},autoplay:null===(n=l.autoplay)||void 0===n||n,loop:null!==(a=l?.loop)&&void 0!==a&&a,scrollbar:{draggable:!0},pagination:!!l?.pagination&&{el:".swiper-pagination",clickable:!0},createElements:!0});new window.Swiper(o(e,".main-swiper"),{spaceBetween:10,thumbs:{swiper:s}})})),e&&i(e,(e=>{new window.Swiper(o(e,".carousel-swiper"),{slidesPerView:3,spaceBetween:10,freeMode:!0,watchSlidesProgress:!0,autoplay:!0,loop:!0,pagination:{el:".swiper-pagination",clickable:!0},navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"}})})))}))})()})();
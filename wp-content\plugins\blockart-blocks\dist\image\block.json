{"$schema": "https://schemas.wp.org/trunk/block.json", "name": "blockart/image", "title": "Image", "description": "Visual communication with your visitor via images.", "keywords": ["image"], "category": "blockart", "textdomain": "blockart", "supports": {"className": false, "customClassName": false}, "example": {"attributes": {}}, "attributes": {"clientId": {"type": "string"}, "link": {"type": "object", "default": {}}, "image": {"type": "object", "default": {}}, "alt1": {"type": "string"}, "alt2": {"type": "string"}, "alignment": {"type": "object", "style": [{"selector": "{{WRAPPER}} {text-align: {{VALUE}}; }"}]}, "objectFit": {"type": "string", "default": "fill", "style": [{"selector": "{{WRAPPER}} img {object-fit: {{VALUE}}; }"}]}, "border": {"type": "object", "default": {"border": 1, "radius": {"desktop": {"lock": true}}, "size": {"desktop": {"lock": true}}}, "style": [{"selector": "{{WRAPPER}} img"}]}, "hoverBorder": {"type": "object", "default": {"border": 1, "radius": {"desktop": {"lock": true}}, "size": {"desktop": {"lock": true}}}, "style": [{"selector": "{{WRAPPER}} img:hover"}]}, "boxShadow": {"type": "object", "default": {"boxShadow": 1}, "style": [{"selector": "{{WRAPPER}} img"}]}, "boxShadowHover": {"type": "object", "default": {"boxShadow": 1}, "style": [{"selector": "{{WRAPPER}} img:hover"}]}, "width": {"type": "object", "style": [{"selector": "{{WRAPPER}} img { width: {{VALUE}} }"}]}, "maxWidth": {"type": "object", "style": [{"selector": "{{WRAPPER}} img { max-width: {{VALUE}} }"}]}, "height": {"type": "object", "style": [{"selector": "{{WRAPPER}} img { height: {{VALUE}} }"}]}, "opacity": {"type": "string", "default": 1, "style": [{"selector": "{{WRAPPER}} img { opacity: {{VALUE}}; }"}]}, "blockMargin": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { margin: {{VALUE}}; }"}]}, "blockPadding": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { padding: {{VALUE}}; }"}]}, "blockZIndex": {"type": "number", "style": [{"selector": "{{WRAPPER}} { z-index: {{VALUE}}; }"}]}, "cssID": {"type": "string"}, "animation": {"type": "string"}, "interaction": {"type": "object"}, "position": {"type": "object"}, "hideOnDesktop": {"type": "boolean", "style": [{"selector": "@media (min-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnTablet": {"type": "boolean", "style": [{"selector": "@media (min-width:48em) and (max-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} { display: none; } }"}]}, "colReverseOnTablet": {"type": "boolean", "style": [{"selector": "@media (max-width:62em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "colReverseOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "blockCSS": {"type": "string"}, "className": {"type": "string"}, "enableCaption": {"type": "boolean", "default": false}, "caption": {"type": "string"}, "hoverEffect": {"type": "string", "default": "static"}, "overlay": {"type": "boolean", "default": false}, "overlayContent": {"type": "string"}, "showHeadingOn": {"type": "string", "default": "always"}, "overlayContentPosition": {"type": "string", "default": "top-center"}, "borderStyle": {"type": "string", "default": "none"}, "overlayBorder": {"type": "object", "default": {"border": 1, "radius": {"desktop": {"lock": true}}, "size": {"desktop": {"lock": true}}}, "style": [{"selector": "{{WRAPPER}}.overlay .overlay-content"}]}, "overlayHeaderColor": {"type": "string", "style": [{"selector": ".blockart-image{{WRAPPER}} .overlay-content h2 {color: {{VALUE}}; }"}]}, "overlayHeaderHoverColor": {"type": "string", "style": [{"selector": ".blockart-image{{WRAPPER}} .overlay-content h2:hover {color: {{VALUE}}; }"}]}, "overlayHeadingTypography": {"type": "object", "default": {"typography": 1}, "style": [{"selector": ".blockart-image{{WRAPPER}} .overlay-content h2"}]}, "overlayHeaderAlignment": {"type": "object", "style": [{"selector": ".blockart-image{{WRAPPER}} .overlay-content h2 {text-align: {{VALUE}}; }"}]}, "overlayHeaderPadding": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": ".blockart-image{{WRAPPER}} .overlay-content h2 {padding : {{VALUE}}; }"}]}, "overlayHeaderMargin": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": ".blockart-image{{WRAPPER}} .overlay-content h2 {margin : {{VALUE}}; }"}]}, "overlayContentColor": {"type": "string", "style": [{"selector": ".blockart-image{{WRAPPER}} .overlay-content figcaption {color: {{VALUE}}; }"}]}, "overlayContentHoverColor": {"type": "string", "style": [{"selector": ".blockart-image{{WRAPPER}} .overlay-content figcaption:hover {color: {{VALUE}}; }"}]}, "overlayContentTypography": {"type": "object", "default": {"typography": 1}, "style": [{"selector": ".blockart-image{{WRAPPER}} .overlay-content figcaption"}]}, "overlayContentAlignment": {"type": "object", "style": [{"selector": ".blockart-image{{WRAPPER}} .overlay-content figcaption {text-align: {{VALUE}}; }"}]}, "overlayContentPadding": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": ".blockart-image{{WRAPPER}} .overlay-content figcaption {padding : {{VALUE}}; }"}]}, "overlayContentMargin": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": ".blockart-image{{WRAPPER}} .overlay-content .overlay-content-figcaption {margin : {{VALUE}}; }"}]}, "maskShape": {"type": "string", "default": "none"}, "maskSize": {"type": "string", "default": "auto"}, "maskPosition": {"type": "string", "default": "center-center"}, "maskRepeat": {"type": "string", "default": "no-repeat"}, "separatorStyle": {"type": "string", "default": "none"}, "separatorPosition": {"type": "string", "default": "after-title"}, "showSeparatorOn": {"type": "string", "default": "always"}, "showDescription": {"type": "string", "default": "always"}, "imageFilter": {"type": "string", "default": "none"}, "icon": {"type": "object", "default": {"enable": false, "icon": ""}}, "iconColor": {"type": "string", "style": [{"selector": ".blockart-image{{WRAPPER}} .overlay-content .overlay-content-figcaption .overlay-icon svg {fill: {{VALUE}}; }"}]}, "iconHoverColor": {"type": "string", "style": [{"selector": ".blockart-image{{WRAPPER}} .overlay-content .overlay-content-figcaption .overlay-icon:hover svg {fill: {{VALUE}}; }"}]}, "iconPosition": {"type": "string", "default": "left"}, "iconSize": {"type": "object", "style": [{"condition": [{"key": "icon", "relation": "!=", "value": ""}], "selector": ".blockart-image{{WRAPPER}} .overlay-content .overlay-content-figcaption .overlay-icon svg { width: {{VALUE}}; height: auto; }"}]}, "iconGap": {"type": "object", "style": [{"condition": [{"key": "icon", "relation": "!=", "value": ""}, {"key": "iconPosition", "relation": "==", "value": "left"}], "selector": ".blockart-image{{WRAPPER}} .overlay-content .overlay-content-figcaption .overlay-icon { margin-right: {{VALUE}}; }"}, {"condition": [{"key": "icon", "relation": "!=", "value": ""}, {"key": "iconPosition", "relation": "==", "value": "right"}], "selector": ".blockart-image{{WRAPPER}} .overlay-content .overlay-content-figcaption .overlay-icon { margin-left: {{VALUE}}; }"}]}}, "style": "blockart-blocks", "editorScript": "blockart-blocks", "editorStyle": "blockart-blocks-editor"}
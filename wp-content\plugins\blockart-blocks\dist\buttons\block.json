{"$schema": "https://schemas.wp.org/trunk/block.json", "name": "blockart/button", "title": "Buttons", "description": "Redirect users to your important pages or websites by clicking button.", "keywords": ["button", "icon"], "category": "blockart", "textdomain": "blockart", "supports": {"className": false, "customClassName": false, "html": false}, "example": {"attributes": {}}, "attributes": {"clientId": {"type": "string"}, "justification": {"type": "object"}, "orientation": {"type": "object"}, "alignment": {"type": "object"}, "flexWrap": {"type": "boolean", "default": true}, "rowGap": {"type": "object", "default": {"desktop": {"value": 8, "unit": "px"}}, "style": [{"selector": "{{WRAPPER}} { row-gap: {{VALUE}}; }"}]}, "columnGap": {"type": "object", "default": {"desktop": {"value": 8, "unit": "px"}}, "style": [{"selector": "{{WRAPPER}} { column-gap: {{VALUE}}; }"}]}, "positionProperty": {"type": "object", "default": {"positionProperty": 1, "position": "none"}, "style": [{"selector": "{{WRAPPER}}"}]}, "blockMargin": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { margin: {{VALUE}}; }"}]}, "blockPadding": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { padding: {{VALUE}}; }"}]}, "blockHTML": {"type": "string", "default": "div"}, "blockZIndex": {"type": "number", "style": [{"selector": "{{WRAPPER}} { z-index: {{VALUE}}; }"}]}, "blockOpacity": {"type": "number", "style": [{"selector": "{{WRAPPER}} { opacity: {{VALUE}}; }"}]}, "cssID": {"type": "string"}, "animation": {"type": "string"}, "interaction": {"type": "object"}, "position": {"type": "object"}, "hideOnDesktop": {"type": "boolean", "style": [{"selector": "@media (min-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnTablet": {"type": "boolean", "style": [{"selector": "@media (min-width:48em) and (max-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} { display: none; } }"}]}, "colReverseOnTablet": {"type": "boolean", "style": [{"selector": "@media (max-width:62em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "colReverseOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "blockCSS": {"type": "string"}, "className": {"type": "string"}}, "style": "blockart-blocks", "editorScript": "blockart-blocks", "editorStyle": "blockart-blocks-editor", "viewScript": "blockart-frontend-common"}
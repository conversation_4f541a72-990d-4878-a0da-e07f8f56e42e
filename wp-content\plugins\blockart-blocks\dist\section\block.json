{"$schema": "https://schemas.wp.org/trunk/block.json", "name": "blockart/section", "title": "Section", "description": "Add Rows and Columns inside rows to create various layouts.", "keywords": ["section", "column", "layout"], "category": "blockart", "textdomain": "blockart", "supports": {"className": false, "customClassName": false, "align": ["center", "wide", "full"]}, "example": {"attributes": {}}, "usesContext": ["clientId", "blockart/queryId", "query", "queryArgs", "queryContext"], "attributes": {"clientId": {"type": "string"}, "hasModal": {"type": "boolean", "default": false}, "modalOnly": {"type": "boolean", "default": false}, "columns": {"type": "number", "default": ""}, "childRow": {"type": "boolean", "default": false}, "horizontalAlignment": {"type": "object", "default": {"desktop": ""}, "style": [{"selector": "{{WRAPPER}} > [class*=\"blockart-container\"] > .blockart-section-inner{ justify-content: {{VALUE}}; }"}]}, "verticalAlignment": {"type": "object", "default": {"desktop": ""}, "style": [{"selector": "{{WRAPPER}} > [class*=\"blockart-container\"] > .blockart-section-inner{ align-items: {{VALUE}}; }"}]}, "container": {"type": "string", "default": "contained"}, "inheritFromTheme": {"type": "boolean", "default": false}, "width": {"type": "object", "default": {"desktop": {"value": 1170, "unit": "px"}}, "style": [{"condition": [{"key": "container", "relation": "==", "value": "contained"}, {"key": "inheritFromTheme", "relation": "!=", "value": true}], "selector": "{{WRAPPER}} > .blockart-container { max-width: {{VALUE}}; }"}]}, "columnGap": {"type": "object", "default": {"desktop": {"value": 30, "unit": "px"}}, "style": [{"selector": "{{WRAPPER}} > [class*=\"blockart-container\"] > .blockart-section-inner { --colSpacing: {{VALUE}}; margin-left: calc(var(--colSpacing) * -1) }"}]}, "rowGap": {"type": "object", "default": {"desktop": {"value": 30, "unit": "px"}}, "style": [{"selector": "{{WRAPPER}} > [class*=\"blockart-container\"] > .blockart-section-inner { row-gap: {{VALUE}}; }"}]}, "height": {"type": "string", "default": "default"}, "minHeight": {"type": "object", "style": [{"condition": [{"key": "height", "relation": "==", "value": "min-height"}], "selector": "{{WRAPPER}} > [class*=\"blockart-container\"]  > .blockart-section-inner {min-height: {{VALUE}};}"}]}, "background": {"type": "object", "default": {"background": 1}, "style": [{"selector": "{{WRAPPER}}.blockart-section"}]}, "hoverBackground": {"type": "object", "default": {"background": 1}, "style": [{"selector": "{{WRAPPER}}:hover"}]}, "border": {"type": "object", "default": {"border": 1, "radius": {"desktop": {"lock": true}}, "size": {"desktop": {"lock": true}}}, "style": [{"selector": "{{WRAPPER}}"}]}, "borderHover": {"type": "object", "default": {"border": 1, "radius": {"desktop": {"lock": true}}, "size": {"desktop": {"lock": true}}}, "style": [{"selector": "{{WRAPPER}}:hover"}]}, "topSeparator": {"type": "object", "default": {"topSeparator": 1, "topSeparatorIcon": "none"}, "style": [{"selector": "{{WRAPPER}} .blockart-top-separator svg"}]}, "bottomSeparator": {"type": "object", "default": {"bottomSeparator": 1, "bottomSeparatorIcon": "none"}, "style": [{"selector": "{{WRAPPER}} .blockart-bottom-separator svg"}]}, "boxShadow": {"type": "object", "default": {"boxShadow": 1}, "style": [{"selector": "{{WRAPPER}}"}]}, "boxShadowHover": {"type": "object", "default": {"boxShadow": 1}, "style": [{"selector": "{{WRAPPER}}:hover"}]}, "overlay": {"type": "boolean", "default": false}, "overlayBackground": {"type": "object", "default": {"background": 1}, "style": [{"condition": [{"key": "overlay", "relation": "==", "value": true}], "selector": "{{WRAPPER}} > .blockart-overlay"}]}, "overlayHoverBackground": {"type": "object", "default": {"background": 1}, "style": [{"condition": [{"key": "overlay", "relation": "==", "value": true}], "selector": "{{WRAPPER}} > .blockart-overlay:hover"}]}, "blockMargin": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { margin: {{VALUE}}; }"}]}, "blockPadding": {"type": "object", "default": {"dimension": 1, "desktop": {"left": 15, "right": 15, "unit": "px"}}, "style": [{"selector": "{{WRAPPER}} { padding: {{VALUE}}; }"}]}, "blockZIndex": {"type": "number", "style": [{"selector": "{{WRAPPER}} { z-index: {{VALUE}}; }"}]}, "cssID": {"type": "string"}, "animation": {"type": "string"}, "interaction": {"type": "object"}, "position": {"type": "object"}, "hideOnDesktop": {"type": "boolean", "style": [{"selector": "@media (min-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnTablet": {"type": "boolean", "style": [{"selector": "@media (min-width:48em) and (max-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} { display: none; } }"}]}, "colReverseOnTablet": {"type": "boolean", "style": [{"selector": "@media (max-width:62em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "colReverseOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "blockCSS": {"type": "string"}, "className": {"type": "string"}}, "style": "blockart-blocks", "editorScript": "blockart-blocks", "editorStyle": "blockart-blocks-editor"}
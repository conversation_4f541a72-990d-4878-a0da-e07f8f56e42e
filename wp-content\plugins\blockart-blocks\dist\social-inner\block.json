{"$schema": "https://schemas.wp.org/trunk/block.json", "name": "blockart/social-inner", "title": "Social Icon", "description": "Share your content through different platforms.", "keywords": ["social share", "social"], "category": "blockart", "textdomain": "blockart", "parent": ["blockart/socials"], "supports": {"className": false, "customClassName": false}, "example": {"attributes": {}}, "attributes": {"clientId": {"type": "string"}, "type": {"type": "string", "default": "facebook"}, "iconType": {"type": "object", "default": "icon", "enum": ["icon", "image"]}, "link": {"type": "object"}, "icon": {"type": "string", "default": "facebook"}, "image": {"type": "object"}, "iconColor": {"type": "string", "style": [{"selector": ".blockart-socials {{WRAPPER}} .blockart-social-link svg {fill: {{VALUE}}; }"}]}, "iconHoverColor": {"type": "string", "style": [{"selector": ".blockart-socials {{WRAPPER}} .blockart-social-link:hover svg {fill: {{VALUE}}; }"}]}, "iconBgColor": {"type": "object", "default": {"background": 1}, "style": [{"selector": ".blockart-socials .blockart-social-inner{{WRAPPER}}"}]}, "iconBgHoverColor": {"type": "object", "default": {"background": 1}, "style": [{"selector": ".blockart-socials .blockart-social-inner{{WRAPPER}}:hover"}]}, "media": {"type": "string"}, "blockMargin": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { margin: {{VALUE}}; }"}]}, "blockPadding": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { padding: {{VALUE}}; }"}]}, "blockZIndex": {"type": "number", "style": [{"selector": "{{WRAPPER}} { z-index: {{VALUE}}; }"}]}, "cssID": {"type": "string"}}, "style": "blockart-blocks", "editorScript": "blockart-blocks", "editorStyle": "blockart-blocks-editor"}
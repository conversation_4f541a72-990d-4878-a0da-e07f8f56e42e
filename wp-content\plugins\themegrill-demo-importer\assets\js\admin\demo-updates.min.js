!function(l,s){var i=l(document),o=s.i18n.__,m=s.i18n._x,d=s.i18n.sprintf;(s=s||{}).updates=s.updates||{},s.updates.importDemo=function(e){var t=l('.demo-import[data-slug="'+e.slug+'"]');return i.find("body").hasClass("full-overlay-active")&&(t=l(".wp-full-overlay-header, .wp-full-overlay-footer").find(".demo-import")),e=_.extend({success:s.updates.importDemoSuccess,error:s.updates.importDemoError},e),t.addClass("updating-message"),t.parents(".theme").addClass("focus"),t.html()!==o("Importing...","themegrill-demo-importer")&&t.data("originaltext",t.html()),t.text(o("Importing...","themegrill-demo-importer")).attr("aria-label",d(m("Importing %s...","demo","themegrill-demo-importer"),t.data("name"))),s.a11y.speak(o("Importing... please wait.","themegrill-demo-importer"),"polite"),l('.install-theme-info, [data-slug="'+e.slug+'"]').removeClass("demo-import-failed").find(".notice.notice-error").remove(),i.trigger("wp-demo-importing",e),s.updates.ajax("import-demo",e)},s.updates.importDemoSuccess=function(e){var t,a=l(".wp-full-overlay-header, .wp-full-overlay-footer, [data-slug="+e.slug+"]");i.trigger("wp-demo-import-success",e),t=a.find(".button-primary").removeClass("updating-message").addClass("updated-message disabled").attr("aria-label",d(m("%s imported!","demo","themegrill-demo-importer"),e.demoName)).text(o("Imported!","themegrill-demo-importer")),s.a11y.speak(o("Import completed successfully.","themegrill-demo-importer"),"polite"),setTimeout(function(){e.previewUrl&&(t.siblings(".demo-preview").remove(),t.attr("target","_blank").attr("href",e.previewUrl).removeClass("demo-import updated-message disabled").addClass("live-preview").attr("aria-label",d(m("Live Preview %s","demo","themegrill-demo-importer"),e.demoName)).text(o("Live Preview","themegrill-demo-importer")))},1e3)},s.updates.importDemoError=function(e){var t,a=d(o("Import failed: %s","themegrill-demo-importer"),e.errorMessage),r=s.updates.adminNotice({className:"update-message notice-error notice-alt",message:a});s.updates.isValidResponse(e,"import")&&!s.updates.maybeHandleCredentialError(e,"import-demo")&&(i.find("body").hasClass("full-overlay-active")?(t=l('.demo-import[data-slug="'+e.slug+'"]'),l(".install-theme-info").prepend(r)):t=l('[data-slug="'+e.slug+'"]').removeClass("focus").addClass("demo-import-failed").append(r).find(".demo-import"),t.removeClass("updating-message").attr("aria-label",d(m("%s import failed","demo","themegrill-demo-importer"),t.data("name"))).text(o("Import Failed!","themegrill-demo-importer")),s.a11y.speak(a,"assertive"),i.trigger("wp-demo-import-error",e))},s.updates.bulkInstallPlugin=function(e){var t,a,r;return e=_.extend({success:s.updates.bulkInstallPluginSuccess,error:s.updates.bulkInstallPluginError},e),i.find("body").hasClass("full-overlay-active")?(t=l('tr[data-slug="'+e.slug+'"]'),a=l('.theme-install-overlay .demo-import[data-slug="'+e.demo+'"]'),r=d(m("Installing %s...","plugin","themegrill-demo-importer"),t.data("name")),t.find(".plugin-status span").addClass("updating-message").attr("aria-label",d(m("Installing %s...","plugin","themegrill-demo-importer"),t.data("name"))).text(o("Installing...","themegrill-demo-importer"))):(a=l('.demo-import[data-slug="'+e.demo+'"]'),r=d(m("Installing %s...","plugin","themegrill-demo-importer"),e.name),a.parents(".theme").addClass("focus")),a.html()!==o("Installing...","themegrill-demo-importer")&&a.data("originaltext",a.html()),a.attr("aria-label",r).addClass("updating-message").text(o("Installing...","themegrill-demo-importer")),s.a11y.speak(o("Installing... please wait.","themegrill-demo-importer"),"polite"),i.trigger("wp-plugin-bulk-installing",e),s.updates.ajax("install-required-plugin",e)},s.updates.bulkInstallPluginSuccess=function(e){l('tr[data-slug="'+e.slug+'"]').removeClass("install").addClass("installed").find(".plugin-status span").removeClass("updating-message install-now").addClass("updated-message active").attr("aria-label",d(m("%s installed!","plugin","themegrill-demo-importer"),e.pluginName)).text(m("Installed!","plugin","themegrill-demo-importer")),s.a11y.speak(o("Installation completed successfully.","themegrill-demo-importer"),"polite"),i.trigger("wp-plugin-bulk-install-success",e)},s.updates.bulkInstallPluginError=function(e){var t,a=l('tr[data-slug="'+e.slug+'"]'),r=a.find(".plugin-status span");s.updates.isValidResponse(e,"install")&&!s.updates.maybeHandleCredentialError(e,"install-plugin")&&(t=d(o("Installation failed: %s","themegrill-demo-importer"),e.errorMessage),r.removeClass("updating-message").addClass("updated-message").attr("aria-label",d(m("%s installation failed","plugin","themegrill-demo-importer"),a.data("name"))).text(o("Installation Failed!","themegrill-demo-importer")),s.a11y.speak(t,"assertive"),i.trigger("wp-plugin-bulk-install-error",e))},s.updates.isValidResponse=function(e,t){var a,r=o("Something went wrong.","themegrill-demo-importer");if(_.isObject(e)&&!_.isFunction(e.always))return!0;switch(_.isString(e)&&"-1"===e?r=o("An error has occurred. Please reload the page and try again.","themegrill-demo-importer"):_.isString(e)?r=e:"undefined"!=typeof e.readyState&&0===e.readyState?r=o("Connection lost or the server is busy. Please try again later.","themegrill-demo-importer"):_.isString(e.statusText)&&(r=e.statusText+' <a href="https://docs.themegrill.com/knowledgebase/demo-import-process-failed/" target="_blank">'+o("Try this solution!","themegrill-demo-importer")+"</a>"),t){case"import":a=o("Import failed: %s","themegrill-demo-importer");break;case"install":a=o("Installation failed: %s","themegrill-demo-importer")}return a=a.replace("%s",r),s.updates.addAdminNotice({id:"unknown_error",className:"notice-error is-dismissible",message:_.unescape(a)}),s.updates.ajaxLocked=!1,s.updates.queue=[],l(".button.updating-message").removeClass("updating-message").removeAttr("aria-label").text(o("Import Failed!","themegrill-demo-importer")),s.a11y.speak(a,"assertive"),!1},s.updates.queueChecker=function(){var e;if(!s.updates.ajaxLocked&&s.updates.queue.length){switch((e=s.updates.queue.shift()).action){case"import-demo":s.updates.importDemo(e.data);break;case"install-plugin":s.updates.bulkInstallPlugin(e.data)}i.trigger("wp-updates-queue-job",e)}}}(jQuery,window.wp);
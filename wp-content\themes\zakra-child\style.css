/*
 Theme Name: Zakra Child
 Theme URI: https://zakratheme.com/
 Description: Child theme for Zakra theme
 Author: Edgaras
 Author URI: https://yourwebsite.com
 Template: zakra
 Version: 1.0.0
*/

/* ==========================================================================
   CUSTOM ANIMATIONS & ENHANCEMENTS
   ========================================================================== */

/* Smooth scroll behavior */
html {
    scroll-behavior: smooth;
}

/* Fade-in animation keyframes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Enhanced button animations */
button,
input[type="button"],
input[type="reset"],
input[type="submit"],
.wp-block-button__link,
.entry-button {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

button:hover,
input[type="button"]:hover,
input[type="reset"]:hover,
input[type="submit"]:hover,
.wp-block-button__link:hover,
.entry-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Ripple effect for buttons */
button::before,
input[type="button"]::before,
input[type="reset"]::before,
input[type="submit"]::before,
.wp-block-button__link::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

button:active::before,
input[type="button"]:active::before,
input[type="reset"]:active::before,
input[type="submit"]:active::before,
.wp-block-button__link:active::before {
    width: 300px;
    height: 300px;
}

/* Enhanced navigation animations */
.zak-primary-nav ul li a {
    position: relative;
    transition: all 0.3s ease;
}

.zak-primary-nav ul li a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: #027ABB;
    transition: width 0.3s ease;
}

.zak-primary-nav ul li a:hover::after,
.zak-primary-nav ul li.current-menu-item a::after {
    width: 100%;
}

/* Card hover animations */
.entry,
.wp-block-group,
.widget {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.entry:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Image hover effects */
.entry-thumbnail img,
.wp-post-image {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.entry-thumbnail:hover img,
.wp-post-image:hover {
    transform: scale(1.05);
}

/* Scroll-triggered animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

/* Stagger animation delays */
.animate-on-scroll:nth-child(1) { transition-delay: 0.1s; }
.animate-on-scroll:nth-child(2) { transition-delay: 0.2s; }
.animate-on-scroll:nth-child(3) { transition-delay: 0.3s; }
.animate-on-scroll:nth-child(4) { transition-delay: 0.4s; }
.animate-on-scroll:nth-child(5) { transition-delay: 0.5s; }
.animate-on-scroll:nth-child(6) { transition-delay: 0.6s; }

/* ==========================================================================
   CUSTOM HOMEPAGE SECTION
   ========================================================================== */

.zakra-custom-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
    overflow: hidden;
}

.zakra-custom-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23027ABB" opacity="0.05"/><circle cx="75" cy="75" r="1" fill="%23027ABB" opacity="0.05"/><circle cx="50" cy="10" r="0.5" fill="%23027ABB" opacity="0.03"/><circle cx="10" cy="60" r="0.5" fill="%23027ABB" opacity="0.03"/><circle cx="90" cy="40" r="0.5" fill="%23027ABB" opacity="0.03"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.custom-section-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    position: relative;
    z-index: 1;
}

.custom-section-text {
    padding-right: 20px;
}

.custom-section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 24px;
    line-height: 1.2;
    background: linear-gradient(135deg, #1e293b 0%, #027ABB 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.custom-section-description {
    font-size: 1.125rem;
    color: #64748b;
    line-height: 1.7;
    margin-bottom: 32px;
}

.custom-section-buttons {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

.custom-btn {
    display: inline-flex;
    align-items: center;
    padding: 14px 28px;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
}

.custom-btn-primary {
    background: linear-gradient(135deg, #027ABB 0%, #0ea5e9 100%);
    color: white;
    box-shadow: 0 4px 14px rgba(2, 122, 187, 0.3);
}

.custom-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(2, 122, 187, 0.4);
    background: linear-gradient(135deg, #0369a1 0%, #0284c7 100%);
}

.custom-btn-secondary {
    background: transparent;
    color: #027ABB;
    border-color: #027ABB;
}

.custom-btn-secondary:hover {
    background: #027ABB;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(2, 122, 187, 0.2);
}

.custom-section-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.image-placeholder {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.image-placeholder:hover {
    transform: scale(1.02) rotate(1deg);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.image-placeholder svg {
    display: block;
    width: 100%;
    height: auto;
}

/* Responsive design */
@media (max-width: 768px) {
    .zakra-custom-section {
        padding: 60px 0;
    }

    .custom-section-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .custom-section-text {
        padding-right: 0;
    }

    .custom-section-title {
        font-size: 2rem;
    }

    .custom-section-buttons {
        justify-content: center;
    }

    .custom-btn {
        padding: 12px 24px;
        font-size: 0.9rem;
    }
}

/* ==========================================================================
   JAVASCRIPT-ENHANCED ANIMATIONS
   ========================================================================== */

/* Image loading animation */
img {
    opacity: 0;
    transition: opacity 0.5s ease;
}

img.loaded {
    opacity: 1;
}

/* Pulse animation for buttons */
.pulse-animation {
    animation: pulse 0.6s ease-in-out;
}

/* Enhanced scroll-based animations */
body.scrolled .zak-header {
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
}

/* Button hover state enhancements */
.btn-hover {
    transform: translateY(-2px) scale(1.02);
}

/* Smooth transitions for all interactive elements */
.zak-icon,
.entry-thumbnail,
.widget-title,
.entry-title {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced focus states for accessibility */
button:focus,
input:focus,
textarea:focus,
select:focus,
a:focus {
    outline: 2px solid #027ABB;
    outline-offset: 2px;
    transition: outline 0.2s ease;
}

/* Loading state for buttons */
.custom-btn.loading {
    position: relative;
    color: transparent;
}

.custom-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Improved mobile touch targets */
@media (max-width: 768px) {
    .custom-btn,
    button,
    input[type="submit"] {
        min-height: 44px;
        min-width: 44px;
    }
}

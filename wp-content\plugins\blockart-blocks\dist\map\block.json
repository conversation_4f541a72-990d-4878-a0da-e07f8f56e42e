{"$schema": "https://schemas.wp.org/trunk/block.json", "name": "blockart/map", "title": "Google Map", "description": "Google Map blocks allows you to add maps.", "keywords": ["map"], "category": "blockart", "textdomain": "blockart", "supports": {"className": false, "customClassName": false}, "example": {"attributes": {}}, "attributes": {"clientId": {"type": "string"}, "address": {"type": "string"}, "coordinates": {"type": "object"}, "mapHeight": {"type": "number", "default": 300}, "language": {"type": "string", "default": "en"}, "zoom": {"type": "number", "default": 10}, "fullscreenControl": {"type": "boolean", "default": true}, "zoomControl": {"type": "boolean", "default": true}, "mapTypeControl": {"type": "boolean", "default": true}, "streetViewControl": {"type": "boolean", "default": true}, "draggable": {"type": "boolean", "default": true}, "marker": {"type": "object"}, "markerIconSize": {"type": "number", "default": 32}, "markerIconColor": {"type": "object"}, "background": {"type": "object", "default": {"background": 1}, "style": [{"selector": "{{WRAPPER}}"}]}, "border": {"type": "object", "default": {"border": 1, "radius": {"desktop": {"lock": true}}, "size": {"desktop": {"lock": true}}}, "style": [{"selector": "{{WRAPPER}}"}]}, "boxShadow": {"type": "object", "default": {"boxShadow": 1}, "style": [{"selector": "{{WRAPPER}}"}]}, "blockMargin": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { margin: {{VALUE}}; }"}]}, "blockPadding": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { padding: {{VALUE}}; }"}]}, "blockZIndex": {"type": "number", "style": [{"selector": "{{WRAPPER}} { z-index: {{VALUE}}; }"}]}, "cssID": {"type": "string"}, "animation": {"type": "string"}, "interaction": {"type": "object"}, "position": {"type": "object"}, "hideOnDesktop": {"type": "boolean", "style": [{"selector": "@media (min-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnTablet": {"type": "boolean", "style": [{"selector": "@media (min-width:48em) and (max-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} { display: none; } }"}]}, "colReverseOnTablet": {"type": "boolean", "style": [{"selector": "@media (max-width:62em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "colReverseOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "blockCSS": {"type": "string"}, "className": {"type": "string"}}, "style": "blockart-blocks", "editorScript": "blockart-blocks", "editorStyle": "blockart-blocks-editor", "viewScript": "blockart-frontend-map"}
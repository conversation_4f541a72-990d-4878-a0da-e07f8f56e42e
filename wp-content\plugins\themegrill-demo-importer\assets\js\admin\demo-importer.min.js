window.wp=window.wp||{},function(c){var n,i,e;(n=wp.demos=wp.demos||{}).data=_demoImporterSettings,i=n.data.l10n,e=_demoImporterSettings.routes,n.isNew=!!n.data.settings.isNew,_.extend(n,{model:{},view:{},routes:{},router:{},template:wp.template}),n.Model=Backbone.Model.extend({initialize:function(){var e;this.set({id:this.get("slug")||this.get("id")}),this.has("sections")&&(e=this.get("sections").description,this.set({description:e}))}}),n.view.Appearance=wp.Backbone.View.extend({el:"#wpbody-content .wrap .theme-browser",window:c(window),page:0,initialize:function(e){_.bindAll(this,"scroller"),this.SearchView=e.SearchView||n.view.Search,this.window.bind("scroll",_.throttle(this.scroller,300))},render:function(){this.view=new n.view.Demos({collection:this.collection,parent:this}),this.search(),this.$el.removeClass("search-loading"),this.view.render(),this.$el.empty().append(this.view.el).addClass("rendered")},searchContainer:c(".search-form"),search:function(){var e;1!==n.data.demos.length&&(e=new this.SearchView({collection:this.collection,parent:this}),(this.SearchView=e).render(),this.searchContainer.append(c.parseHTML('<label class="screen-reader-text" for="wp-filter-search-input">'+i.search+"</label>")).append(e.el).on("submit",function(e){e.preventDefault()}))},scroller:function(){var e=this,e=(this.window.scrollTop(),e.window.height(),e.$el.offset().top+e.$el.outerHeight(!1)-e.window.height());Math.round(.9*e)}}),n.Collection=Backbone.Collection.extend({model:n.Model,terms:"",doSearch:function(e){this.terms!==e&&(this.terms=e,0<this.terms.length&&this.search(this.terms),""===this.terms&&(this.reset(n.data.demos),c("body").removeClass("no-results")),this.trigger("demos:update"))},search:function(t){var i,e,o,s,r;this.reset(n.data.demos,{silent:!0}),t=(t=t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")).replace(/ /g,")(?=.*"),i=new RegExp("^(?=.*"+t+").+","i"),0===(e=this.filter(function(e){return o=e.get("name").replace(/(<([^>]+)>)/gi,""),s=e.get("description").replace(/(<([^>]+)>)/gi,""),r=e.get("author").replace(/(<([^>]+)>)/gi,""),o=_.union([o,e.get("id"),s,r,e.get("tags")]),i.test(e.get("author"))&&2<t.length&&e.set("displayAuthor",!0),i.test(o)})).length?this.trigger("query:empty"):c("body").removeClass("no-results"),this.reset(e)},paginate:function(e){return this},count:!1,query:function(t){var e,i,o,s=this.queries,r=this;if(this.currentQuery.request=t,e=_.find(s,function(e){return _.isEqual(e.request,t)}),(i=_.has(t,"page"))||(this.currentQuery.page=1),e||i){if(i)return this.apiCall(t,i).done(function(e){r.add(e.demos),r.trigger("query:success"),r.loadingDemos=!1}).fail(function(){r.trigger("query:fail")});0===e.demos.length?r.trigger("query:empty"):c("body").removeClass("no-results"),_.isNumber(e.total)&&(this.count=e.total),this.reset(e.demos),e.total||(this.count=this.length),this.trigger("demos:update"),this.trigger("query:success",this.count)}else this.apiCall(t).done(function(e){e.demos&&(r.reset(e.demos),o=e.info.results,s.push({demos:e.demos,request:t,total:o})),r.trigger("demos:update"),r.trigger("query:success",o),e.demos&&0===e.demos.length&&r.trigger("query:empty")}).fail(function(){r.trigger("query:fail")})},queries:[],currentQuery:{page:1,request:{}},apiCall:function(e,t){return wp.ajax.send("query-demos",{data:{request:_.extend({per_page:100},e)},beforeSend:function(){t||c("body").addClass("loading-content").removeClass("no-results")}})},loadingDemos:!1}),n.view.Demo=wp.Backbone.View.extend({className:"theme",state:"grid",html:n.template("demo"),events:{click:"preview",keydown:"preview",touchend:"preview",keyup:"addFocus",touchmove:"preventExpand","click .demo-import":"importDemo"},touchDrag:!1,initialize:function(){this.model.on("change",this.render,this)},render:function(){var e=this.model.toJSON();this.$el.html(this.html(e)).attr({tabindex:0,"aria-describedby":e.id+"-action "+e.id+"-name","data-slug":e.id}),this.activeDemo(),this.model.get("displayAuthor")&&this.$el.addClass("display-author")},activeDemo:function(){this.model.get("active")&&this.$el.addClass("active")},addFocus:function(){var e=c(":focus").hasClass("theme")?c(":focus"):c(":focus").parents(".theme");c(".theme.focus").removeClass("focus"),e.addClass("focus")},preventExpand:function(){this.touchDrag=!0},preview:function(e){var t,i,o=this;if(e=e||window.event,!0===this.touchDrag)return this.touchDrag=!1;c(e.target).not(".install-demo-preview").parents(".theme-actions").length||"keydown"===e.type&&13!==e.which&&32!==e.which||"keydown"===e.type&&13!==e.which&&c(":focus").hasClass("button")||(e.preventDefault(),e=e||window.event,n.focusedDemo=this.$el,n.preview=i=new n.view.Preview({model:this.model}),i.render(),this.setNavButtonsState(),1===this.model.collection.length?i.$el.addClass("no-navigation"):i.$el.removeClass("no-navigation"),c("div.wrap").append(i.el),this.listenTo(i,"demo:next",function(){if(t=o.model,_.isUndefined(o.current)||(t=o.current),o.current=o.model.collection.at(o.model.collection.indexOf(t)+1),_.isUndefined(o.current))return o.options.parent.parent.trigger("demo:end"),o.current=t;i.model=o.current,i.render(),this.setNavButtonsState(),c(".next-theme").focus()}).listenTo(i,"demo:previous",function(){t=o.model,0===o.model.collection.indexOf(o.current)||(_.isUndefined(o.current)||(t=o.current),o.current=o.model.collection.at(o.model.collection.indexOf(t)-1),_.isUndefined(o.current))||(i.model=o.current,i.render(),this.setNavButtonsState(),c(".previous-theme").focus())}),this.listenTo(i,"preview:close",function(){o.current=o.model}))},setNavButtonsState:function(){var e=c(".theme-install-overlay"),t=_.isUndefined(this.current)?this.model:this.current,i=e.find(".previous-theme"),e=e.find(".next-theme");0===this.model.collection.indexOf(t)&&(i.addClass("disabled").prop("disabled",!0),e.focus()),_.isUndefined(this.model.collection.at(this.model.collection.indexOf(t)+1))&&(e.addClass("disabled").prop("disabled",!0),i.focus())},importDemo:function(e){var t=this,i=c(e.target),o=c(e.target).data("plugins");e.preventDefault(),i.hasClass("disabled")||i.hasClass("updating-message")||c.confirm({title:"",content:'<div class="demo-import-confirm-message">'+(t.model.get("ceAddonNotice")?wp.demos.data.settings.ceAddonNotice:"")+wp.demos.data.settings.confirmImport+"</div>",boxWidth:"50%",useBootstrap:!1,buttons:{confirm:{text:wp.demos.data.l10n.confirmMsg,keys:["enter"],btnClass:t.model.get("ceAddonNotice")?"disabled demo-import-confirm-button":"demo-import-confirm-button",action:function(){t.processImport(i,o,t)}},cancel:{btnClass:"demo-import-cancel-button",action:function(){}}}})},processImport:function(i,e,o){wp.updates.maybeRequestFilesystemCredentials(event),c(document).trigger("wp-plugin-bulk-install",e),c.each(e,function(e,t){t.is_active||wp.updates.queue.push({action:"install-plugin",data:{plugin:t.slug,name:t.name,slug:e,demo:i.data("slug")}})}),c(document).on("wp-demo-import-success",function(e,t){o.model.get("id")===t.slug&&(o.render(),o.model.set({imported:!0}))}),wp.updates.queue.push({action:"import-demo",data:{slug:i.data("slug")}}),wp.updates.queueChecker()}}),n.view.Preview=wp.Backbone.View.extend({className:"wp-full-overlay expanded",el:".theme-install-overlay",events:{"click .close-full-overlay":"close","click .collapse-sidebar":"collapse","click .devices button":"previewDevice","click .previous-theme":"previousDemo","click .next-theme":"nextDemo",keyup:"keyEvent","click .demo-import":"importDemo"},html:n.template("demo-preview"),render:function(){var e=this,t=this.model.toJSON(),i=c(document.body);i.attr("aria-busy","true"),this.$el.removeClass("iframe-ready").html(this.html(t)),(t=this.$el.data("current-preview-device"))&&e.tooglePreviewDeviceButtons(t),n.router.navigate(n.router.baseUrl(n.router.demoPath+this.model.get("id")),{replace:!1}),this.$el.fadeIn(200,function(){i.addClass("demo-importer-active full-overlay-active")}),this.$el.find("iframe").one("load",function(){e.iframeLoaded()})},iframeLoaded:function(){this.$el.addClass("iframe-ready"),c(document.body).attr("aria-busy","false")},close:function(){return this.$el.fadeOut(200,function(){c("body").removeClass("demo-importer-active full-overlay-active"),n.focusedDemo&&n.focusedDemo.focus()}).removeClass("iframe-ready"),n.router.selectedTab?n.router.navigate(n.router.baseUrl("&browse="+n.router.selectedTab)):n.router.navigate(n.router.baseUrl("")),this.trigger("preview:close"),this.undelegateEvents(),this.unbind(),!1},collapse:function(e){e=c(e.currentTarget);return"true"===e.attr("aria-expanded")?e.attr({"aria-expanded":"false","aria-label":i.expandSidebar}):e.attr({"aria-expanded":"true","aria-label":i.collapseSidebar}),this.$el.toggleClass("collapsed").toggleClass("expanded"),!1},previewDevice:function(e){e=c(e.currentTarget).data("device");this.$el.removeClass("preview-desktop preview-tablet preview-mobile").addClass("preview-"+e).data("current-preview-device",e),this.tooglePreviewDeviceButtons(e)},tooglePreviewDeviceButtons:function(e){var t=c(".wp-full-overlay-footer .devices");t.find("button").removeClass("active").attr("aria-pressed",!1),t.find("button.preview-"+e).addClass("active").attr("aria-pressed",!0)},keyEvent:function(e){27===e.keyCode&&(this.undelegateEvents(),this.close()),39===e.keyCode&&_.once(this.nextDemo()),37===e.keyCode&&this.previousDemo()},nextDemo:function(){return this.trigger("demo:next",this.model.cid),!1},previousDemo:function(){return this.trigger("demo:previous",this.model.cid),!1},importDemo:function(e){var t=this,i=c(e.target),o=c(".plugins-list-table").find("#the-list tr"),s=[];e.preventDefault(),i.hasClass("disabled")||i.hasClass("updating-message")||c.confirm({title:"",content:'<div class="demo-import-confirm-message">'+wp.demos.data.settings.confirmImport+"</div>",boxWidth:"50%",useBootstrap:!1,buttons:{confirm:{text:wp.demos.data.l10n.confirmMsg,keys:["enter"],btnClass:"demo-import-confirm-button",action:function(){t.processImport(i,o,0,0,s,t)}},cancel:{btnClass:"demo-import-cancel-button",action:function(){}}},onContentReady:function(){c("body").addClass("demo-import-message-popup")},onDestroy:function(){c("body").removeClass("demo-import-message-popup")}})},processImport:function(s,e,r,n,a,l){wp.updates.maybeRequestFilesystemCredentials(event),c(".theme-install-overlay").find(".next-theme, .previous-theme").addClass("disabled"),c(document).trigger("wp-plugin-bulk-install",e),e.each(function(e,t){t=c(t);t.hasClass("inactive")&&!t.find("notice-error").length&&wp.updates.queue.push({action:"install-plugin",data:{plugin:t.data("plugin"),slug:t.data("slug"),demo:s.data("slug")}})}),c(document).on("wp-plugin-bulk-installing",function(){c(".wp-full-overlay-sidebar-content").animate({scrollTop:c(document).height()})}),c(document).on("wp-plugin-bulk-install-success wp-plugin-bulk-install-error",function(e,t){var i,o=c('[data-slug="'+t.slug+'"]');"wp-"+t.install+"-bulk-install-success"===e.type?r++:(e=t.pluginName||o.find(".plugin-name").text(),n++,a.push(e+": "+t.errorMessage)),wp.updates.adminNotice=wp.template("wp-bulk-installs-admin-notice"),c(".plugins-details .bulk-action-notice").remove(),c(".plugins-details .plugins-info").after(wp.updates.adminNotice({id:"bulk-action-notice",className:"bulk-action-notice notice-alt",successes:r,errors:n,errorMessages:a,type:t.install})),i=c("#bulk-action-notice").on("click","button",function(){c(this).toggleClass("bulk-action-errors-collapsed").attr("aria-expanded",!c(this).hasClass("bulk-action-errors-collapsed")),i.find(".bulk-action-errors").toggleClass("hidden")}),wp.updates.queue.length||(0<n?s.removeClass("updating-message").text(s.data("originaltext")):(l.model.set({requiredPlugins:!1}),c(".theme-install-overlay").find(".next-theme, .previous-theme").addClass("disabled")))}),c(document).on("wp-updates-notice-added",function(){wp.updates.adminNotice=wp.template("wp-updates-admin-notice")}),c(document).on("wp-demo-import-success",function(e,t){l.model.get("id")===t.slug&&l.model.set({imported:!0})}),wp.updates.queue.push({action:"import-demo",data:{slug:s.data("slug")}}),wp.updates.queueChecker()}}),n.view.Demos=wp.Backbone.View.extend({className:"themes wp-clearfix",$overlay:c("div.theme-overlay"),index:0,count:c(".wrap .demo-count"),liveDemoCount:0,initialize:function(e){var t=this;this.parent=e.parent,this.setView("grid"),t.importedDemo(),this.listenTo(t.collection,"demos:update",function(){t.parent.page=0,t.importedDemo(),t.render(this)}),this.listenTo(t.collection,"query:success",function(e){_.isNumber(e)?(t.count.text(e),t.announceSearchResults(e)):(t.count.text(t.collection.length),t.announceSearchResults(t.collection.length))}),this.listenTo(t.collection,"query:empty",function(){c("body").addClass("no-results")}),this.listenTo(this.parent,"demo:scroll",function(){t.renderDemos(t.parent.page)})},render:function(){this.$el.empty(),0<this.options.collection.size()&&this.renderDemos(this.parent.page),this.liveDemoCount=this.collection.count||this.collection.length,this.count.text(this.liveDemoCount)},renderDemos:function(e){var t=this;t.instance=t.collection.paginate(e),0===t.instance.size()?this.parent.trigger("demo:end"):(n.isNew&&1<=e&&c(".add-new-theme").remove(),t.instance.each(function(e){t.demo=new n.view.Demo({model:e,parent:t}),t.demo.render(),t.$el.append(t.demo.el)}),n.isNew&&n.data.settings.suggestURI&&this.$el.append('<div class="theme add-new-theme"><a href="'+n.data.settings.suggestURI+'" target="blank"><div class="theme-screenshot"><span></span></div><h2 class="theme-name">'+i.suggestNew+"</h2></a></div>"),this.parent.page++)},importedDemo:function(){var e=this.collection.findWhere({active:!0});e&&(this.collection.remove(e),this.collection.add(e,{at:0}))},setView:function(e){return e},announceSearchResults:function(e){0===e?wp.a11y.speak(i.noDemosFound):wp.a11y.speak(i.demosFound.replace("%d",e))}}),n.view.Search=wp.Backbone.View.extend({tagName:"input",className:"wp-filter-search",id:"wp-filter-search-input",searching:!1,attributes:{placeholder:i.searchPlaceholder,type:"search","aria-describedby":"live-search-desc"},events:{input:"search",keyup:"search",blur:"pushState"},initialize:function(e){this.parent=e.parent,this.listenTo(this.parent,"demo:close",function(){this.searching=!1})},search:function(e){"keyup"===e.type&&27===e.which&&(e.target.value=""),this.doSearch(e)},doSearch:function(e){var t={};this.collection.doSearch(e.target.value.replace(/\+/g," ")),this.searching&&13!==e.which?t.replace=!0:this.searching=!0,e.target.value?n.router.navigate(n.router.baseUrl(n.router.searchPath+e.target.value),t):n.router.navigate(n.router.baseUrl(""))},pushState:function(e){var t=n.router.baseUrl("");e.target.value&&(t=n.router.baseUrl(n.router.searchPath+encodeURIComponent(e.target.value))),this.searching=!1,n.router.navigate(t)}}),n.Router=Backbone.Router.extend({routes:e,baseUrl:function(e){return _demoImporterSettings.baseURL+e},demoPath:"&demo=",browsePath:"&browse=",searchPath:"&search=",search:function(e){c(".wp-filter-search").val(e.replace(/\+/g," "))},navigate:function(e,t){Backbone.history._hasPushState&&Backbone.Router.prototype.navigate.call(this,e,t)}}),n.view.InstallerSearch=n.view.Search.extend({events:{input:"search",keyup:"search"},terms:"",search:function(e){("keyup"!==e.type||9!==e.which&&16!==e.which)&&(this.collection=this.options.parent.view.collection,"keyup"===e.type&&27===e.which&&(e.target.value=""),this.doSearch(e.target.value))},doSearch:function(e){this.terms!==e&&(this.terms=e,c(".filter-links li > a.current").removeClass("current").removeAttr("aria-current"),this.collection.doSearch(e.replace(/\+/g," ")),n.router.navigate(n.router.baseUrl(n.router.searchPath+encodeURIComponent(e)),{replace:!0}))}}),n.view.Installer=n.view.Appearance.extend({el:"#wpbody-content .wrap",events:{"click .filter-links li > a":"onSort"},render:function(){var e=this;this.search(),this.collection=new n.Collection,this.listenTo(this,"demo:end",function(){e.collection.loadingDemos||(e.collection.loadingDemos=!0,e.collection.currentQuery.page++,_.extend(e.collection.currentQuery.request,{page:e.collection.currentQuery.page}),e.collection.query(e.collection.currentQuery.request))}),this.listenTo(this.collection,"query:success",function(){c("body").removeClass("loading-content"),c(".theme-browser").find("div.error").remove()}),this.listenTo(this.collection,"query:fail",function(){c("body").removeClass("loading-content"),c(".theme-browser").find("div.error").remove(),c(".theme-browser").find("div.themes").before('<div class="error"><p>'+i.error+'</p><p><button class="button try-again">'+i.tryAgain+"</button></p></div>"),c(".theme-browser .error .try-again").on("click",function(e){e.preventDefault(),c("input.wp-filter-search").trigger("input")})}),this.view&&this.view.remove(),this.view=new n.view.Demos({collection:this.collection,parent:this}),this.page=0,this.$el.find(".themes").remove(),this.view.render(),this.$el.find(".theme-browser").append(this.view.el).addClass("rendered")},browse:function(e,t){this.collection.query({browse:e,builder:t})},onSort:function(e){var t=c(e.target),i=t.data("sort"),o=t.data("type");e.preventDefault(),i=i||n.router.selectedTab,o=o||n.router.selectedType,t.hasClass(this.activeClass)||(this.sort(i,o),n.router.navigate(n.router.baseUrl(n.router.browsePath+i)))},sort:function(e,t){this.clearSearch(),n.router.selectedTab=e,n.router.selectedType=t,c(".filter-links li > a").removeClass(this.activeClass).removeAttr("aria-current"),c('[data-sort="'+e+'"]').addClass(this.activeClass).attr("aria-current","page"),c('[data-type="'+t+'"]').addClass(this.activeClass).attr("aria-current","page"),this.browse(e,t)},activeClass:"current",clearSearch:function(){c("#wp-filter-search-input").val("")}}),n.RunInstaller={init:function(){this.view=new n.view.Installer({section:"all",SearchView:n.view.InstallerSearch}),this.render(),this.view.SearchView.doSearch=_.debounce(this.view.SearchView.doSearch,500)},render:function(){this.view.render(),this.routes(),Backbone.History.started&&Backbone.history.stop(),Backbone.history.start({root:n.data.settings.adminUrl,pushState:!0,hashChange:!1})},routes:function(){var i=this,t={};n.router=new n.Router,n.router.on("route:preview",function(e){n.preview&&(n.preview.undelegateEvents(),n.preview.unbind()),i.view.view.demo&&i.view.view.demo.preview?(i.view.view.demo.model=i.view.collection.findWhere({slug:e}),i.view.view.demo.preview()):(t.demo=e,i.view.collection.query(t),i.view.collection.trigger("update"),i.view.collection.once("query:success",function(){c('div[data-slug="'+e+'"]').trigger("click")}))}),n.router.on("route:sort",function(e){var t=n.router.selectedType||c(".filter-links.pagebuilders li").first().find("a").data("type");e&&c('[data-sort="'+e+'"]').length||(e="all",n.router.navigate(n.router.baseUrl("&browse=all"),{replace:!0})),i.view.sort(e,t),n.preview&&n.preview.close()}),n.router.on("route:search",function(){c(".wp-filter-search").focus().trigger("keyup")}),this.extraRoutes()},extraRoutes:function(){return!1}},c(document).ready(function(){n.RunInstaller.init(),c(document.body).on("init_tooltips",function(){c("#tiptip_holder").removeAttr("style"),c("#tiptip_arrow").removeAttr("style"),c(".tips").tipTip({attribute:"data-tip",defaultPosition:"top",fadeIn:50,fadeOut:50,delay:50})}).trigger("init_tooltips"),c(".themegrill-demo-importer-rating-link").on("click",function(){var e=c(this);c.post(n.data.settings.ajaxUrl,{action:"footer-text-rated"}),e.parent().text(e.data("rated"))})})}(jQuery);
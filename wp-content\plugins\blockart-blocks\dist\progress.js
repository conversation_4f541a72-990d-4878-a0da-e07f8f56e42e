(()=>{var t={};t.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),(()=>{var r;t.g.importScripts&&(r=t.g.location+"");var e=t.g.document;if(!r&&e&&(e.currentScript&&"SCRIPT"===e.currentScript.tagName.toUpperCase()&&(r=e.currentScript.src),!r)){var i=e.getElementsByTagName("script");if(i.length)for(var a=i.length-1;a>-1&&(!r||!/^http(s?):/.test(r));)r=i[a--].src}if(!r)throw new Error("Automatic publicPath is not supported in this browser");r=r.replace(/^blob:/,"").replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),t.p=r})(),t.p=window._BLOCKART_WEBPACK_PUBLIC_PATH_,(()=>{const{$$:t,domReady:r,observeElementInView:e,each:i,toArray:a}=window.blockartUtils;r((()=>{t(".blockart--with-animation").length&&i(a(t(".blockart--with-animation")),(t=>{t.classList.contains("blockart-animate")||e(t,(function(){t.classList.add("blockart-animate")}))}))}))})()})();
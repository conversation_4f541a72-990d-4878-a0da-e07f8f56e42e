(()=>{var e,t,r={},o={};function i(e){var t=o[e];if(void 0!==t)return t.exports;var n=o[e]={exports:{}};return r[e](n,n.exports,i),n.exports}i.m=r,i.d=(e,t)=>{for(var r in t)i.o(t,r)&&!i.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},i.f={},i.e=e=>Promise.all(Object.keys(i.f).reduce(((t,r)=>(i.f[r](e,t),t)),[])),i.u=e=>e+".js",i.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),e={},t="blockart-blocks:",i.l=(r,o,n,a)=>{if(e[r])e[r].push(o);else{var l,c;if(void 0!==n)for(var s=document.getElementsByTagName("script"),p=0;p<s.length;p++){var u=s[p];if(u.getAttribute("src")==r||u.getAttribute("data-webpack")==t+n){l=u;break}}l||(c=!0,(l=document.createElement("script")).charset="utf-8",l.timeout=120,i.nc&&l.setAttribute("nonce",i.nc),l.setAttribute("data-webpack",t+n),l.src=r),e[r]=[o];var d=(t,o)=>{l.onerror=l.onload=null,clearTimeout(b);var i=e[r];if(delete e[r],l.parentNode&&l.parentNode.removeChild(l),i&&i.forEach((e=>e(o))),t)return t(o)},b=setTimeout(d.bind(null,void 0,{type:"timeout",target:l}),12e4);l.onerror=d.bind(null,l.onerror),l.onload=d.bind(null,l.onload),c&&document.head.appendChild(l)}},i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e;i.g.importScripts&&(e=i.g.location+"");var t=i.g.document;if(!e&&t&&(t.currentScript&&"SCRIPT"===t.currentScript.tagName.toUpperCase()&&(e=t.currentScript.src),!e)){var r=t.getElementsByTagName("script");if(r.length)for(var o=r.length-1;o>-1&&(!e||!/^http(s?):/.test(e));)e=r[o--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/^blob:/,"").replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),i.p=e})(),(()=>{var e={970:0};i.f.j=(t,r)=>{var o=i.o(e,t)?e[t]:void 0;if(0!==o)if(o)r.push(o[2]);else{var n=new Promise(((r,i)=>o=e[t]=[r,i]));r.push(o[2]=n);var a=i.p+i.u(t),l=new Error;i.l(a,(r=>{if(i.o(e,t)&&(0!==(o=e[t])&&(e[t]=void 0),o)){var n=r&&("load"===r.type?"missing":r.type),a=r&&r.target&&r.target.src;l.message="Loading chunk "+t+" failed.\n("+n+": "+a+")",l.name="ChunkLoadError",l.type=n,l.request=a,o[1](l)}}),"chunk-"+t,t)}};var t=(t,r)=>{var o,n,[a,l,c]=r,s=0;if(a.some((t=>0!==e[t]))){for(o in l)i.o(l,o)&&(i.m[o]=l[o]);c&&c(i)}for(t&&t(r);s<a.length;s++)n=a[s],i.o(e,n)&&e[n]&&e[n][0](),e[n]=0},r=self.webpackChunkblockart_blocks=self.webpackChunkblockart_blocks||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),i.p=window._BLOCKART_WEBPACK_PUBLIC_PATH_,(()=>{const{$$:e,domReady:t,toArray:r,each:o}=window.blockartUtils;t((()=>{const t=r(e('lottie-player[id^="blockart-lottie"]'));t.length&&i.e(397).then(i.bind(i,397)).then((()=>{const e=t.filter((e=>e.hasAttribute("viewport"))),r=t.filter((e=>e.hasAttribute("scroll")));(e.length||r.length)&&i.e(968).then(i.bind(i,968)).then((({create:t})=>{o(e,(e=>{t({player:e.getLottie(),mode:"scroll",actions:[{visibility:[0,1],type:"play"}]})})),o(r,(e=>{t({player:e.getLottie(),mode:"scroll",actions:[{visibility:[0,1],type:"seek",frames:[0,100]}]})}))}))}))}))})()})();
{"$schema": "https://schemas.wp.org/trunk/block.json", "name": "blockart/icon-list-item", "title": "Icon List Item", "description": "Create a list with icons.", "keywords": ["icon list", "list"], "category": "blockart", "textdomain": "blockart", "parent": ["blockart/icon-list"], "supports": {"className": false, "customClassName": false}, "example": {"attributes": {}}, "attributes": {"clientId": {"type": "string"}, "text": {"type": "string", "default": "List Item"}, "color": {"type": "string", "style": [{"selector": ".blockart-icon-list {{WRAPPER}} .blockart-list-label {color: {{VALUE}}; }"}]}, "hoverColor": {"type": "string", "style": [{"selector": ".blockart-icon-list {{WRAPPER}}:hover .blockart-list-label {color: {{VALUE}}; }"}]}, "borderColor": {"type": "string", "style": [{"selector": ".blockart-icon-list {{WRAPPER}} .blockart-icon-wrapper {border-color: {{VALUE}} !important; }"}]}, "borderHoverColor": {"type": "string", "style": [{"selector": ".blockart-icon-list {{WRAPPER}} .blockart-icon-wrapper:hover {border-color: {{VALUE}} !important; }"}]}, "margin": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": ".blockart-icon-list {{WRAPPER}} { margin: {{VALUE}}; }"}]}, "blockMargin": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { margin: {{VALUE}}; }"}]}, "padding": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": ".blockart-icon-list {{WRAPPER}} { padding: {{VALUE}}; }"}]}, "blockPadding": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { padding: {{VALUE}}; }"}]}, "blockZIndex": {"type": "number", "style": [{"selector": "{{WRAPPER}} { z-index: {{VALUE}}; }"}]}, "cssID": {"type": "string"}, "animation": {"type": "string"}, "interaction": {"type": "object"}, "position": {"type": "object"}, "hideOnDesktop": {"type": "boolean", "style": [{"selector": "@media (min-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnTablet": {"type": "boolean", "style": [{"selector": "@media (min-width:48em) and (max-width:62em) { {{WRAPPER}} { display: none; } }"}]}, "hideOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} { display: none; } }"}]}, "colReverseOnTablet": {"type": "boolean", "style": [{"selector": "@media (max-width:62em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "colReverseOnMobile": {"type": "boolean", "style": [{"selector": "@media (max-width:48em) { {{WRAPPER}} > .blockart-container > .blockart-section-inner { flex-direction:column-reverse; } }"}]}, "blockCSS": {"type": "string"}, "className": {"type": "string"}, "image": {"type": "object"}, "link": {"type": "object"}, "iconType": {"type": "object", "default": "icon", "enum": ["icon", "image", "none"]}, "icon": {"type": "string", "default": ""}, "parentIcon": {"type": "string", "default": "check"}, "parentIconPosition": {"type": "string", "default": "left"}, "parentToggleLabels": {"type": "boolean", "default": false}, "iconColor": {"type": "string", "style": [{"selector": "{{WRAPPER}} .blockart-icon-wrapper svg {fill: {{VALUE}} !important; }"}]}, "iconHoverColor": {"type": "string", "style": [{"selector": "{{WRAPPER}}:hover .blockart-icon-wrapper svg {fill: {{VALUE}} !important; }"}]}, "iconSize": {"type": "number", "style": [{"selector": "{{WRAPPER}} .blockart-icon-wrapper svg { width: {{VALUE}}px !important; height: {{VALUE}}px !important; }"}]}, "iconOpacity": {"type": "number", "style": [{"selector": "{{WRAPPER}} .blockart-icon-wrapper svg { opacity: {{VALUE}}; }"}]}, "iconRotation": {"type": "number", "style": [{"selector": "{{WRAPPER}} .blockart-icon-wrapper svg { transform: rotate({{VALUE}}deg); }"}]}, "border": {"type": "object", "default": {"border": 1, "radius": {"desktop": {"lock": true}}, "size": {"desktop": {"lock": true}}}, "style": [{"selector": "{{WRAPPER}}"}]}, "hoverBorder": {"type": "object", "default": {"border": 1, "radius": {"desktop": {"lock": true}}, "size": {"desktop": {"lock": true}}}, "style": [{"selector": "{{WRAPPER}}:hover"}]}, "boxShadow": {"type": "object", "default": {"boxShadow": 1}, "style": [{"selector": "{{WRAPPER}}"}]}, "boxShadowHover": {"type": "object", "default": {"boxShadow": 1}, "style": [{"selector": "{{WRAPPER}}:hover"}]}}, "style": "blockart-blocks", "editorScript": "blockart-blocks", "editorStyle": "blockart-blocks-editor"}
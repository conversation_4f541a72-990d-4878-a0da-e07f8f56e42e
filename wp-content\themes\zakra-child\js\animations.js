/**
 * Custom animations for Zakra Child Theme
 * Handles scroll-triggered animations and interactive effects
 */

(function($) {
    'use strict';

    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(function(entry) {
            if (entry.isIntersecting) {
                entry.target.classList.add('animated');
                // Unobserve after animation to prevent re-triggering
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Initialize animations when document is ready
    $(document).ready(function() {
        
        // Observe all elements with animate-on-scroll class
        $('.animate-on-scroll').each(function() {
            observer.observe(this);
        });

        // Add smooth scrolling to anchor links
        $('a[href^="#"]').on('click', function(e) {
            const target = $(this.getAttribute('href'));
            if (target.length) {
                e.preventDefault();
                $('html, body').animate({
                    scrollTop: target.offset().top - 80
                }, 800, 'easeInOutCubic');
            }
        });

        // Enhanced button hover effects
        $('.custom-btn, button, input[type="submit"]').hover(
            function() {
                $(this).addClass('btn-hover');
            },
            function() {
                $(this).removeClass('btn-hover');
            }
        );

        // Parallax effect for custom section background
        $(window).scroll(function() {
            const scrolled = $(window).scrollTop();
            const parallax = $('.zakra-custom-section');
            
            if (parallax.length) {
                const speed = scrolled * 0.5;
                parallax.css('background-position', 'center ' + speed + 'px');
            }
        });

        // Add loading animation to images
        $('img').each(function() {
            const img = $(this);
            if (!img.hasClass('loaded')) {
                img.on('load', function() {
                    img.addClass('loaded');
                }).each(function() {
                    if (this.complete) {
                        $(this).trigger('load');
                    }
                });
            }
        });

        // Stagger animation for multiple elements
        $('.animate-on-scroll').each(function(index) {
            $(this).css('transition-delay', (index * 0.1) + 's');
        });

        // Add pulse animation to important buttons
        $('.custom-btn-primary').hover(function() {
            $(this).addClass('pulse-animation');
            setTimeout(() => {
                $(this).removeClass('pulse-animation');
            }, 600);
        });

        // Smooth reveal for content sections
        $('.entry, .widget').each(function() {
            $(this).addClass('animate-on-scroll');
            observer.observe(this);
        });

    });

    // Add easing function for smooth scrolling
    $.easing.easeInOutCubic = function(x, t, b, c, d) {
        if ((t /= d / 2) < 1) return c / 2 * t * t * t + b;
        return c / 2 * ((t -= 2) * t * t + 2) + b;
    };

    // Performance optimization: throttle scroll events
    function throttle(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Optimized scroll handler
    const handleScroll = throttle(function() {
        // Add any additional scroll-based animations here
        const scrollTop = $(window).scrollTop();
        
        // Add scroll-based class to body for CSS animations
        if (scrollTop > 100) {
            $('body').addClass('scrolled');
        } else {
            $('body').removeClass('scrolled');
        }
    }, 16); // ~60fps

    $(window).on('scroll', handleScroll);

})(jQuery);

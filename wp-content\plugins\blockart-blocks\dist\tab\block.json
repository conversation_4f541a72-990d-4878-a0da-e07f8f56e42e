{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 2, "title": "Tab", "name": "blockart/tab", "category": "blockart", "parent": ["blockart/tabs"], "attributes": {"clientId": {"type": "string"}, "tabNumber": {"type": "number"}, "panelWidth": {"type": "object", "style": [{"selector": "{{WRAPPER}} { flex-basis: {{VALUE}}%; width: {{VALUE}}%; }"}]}, "panelBackground": {"type": "object", "default": {"background": 1}, "style": [{"selector": "{{WRAPPER}}"}]}, "hoverPanelBackground": {"type": "object", "default": {"background": 1}, "style": [{"selector": "{{WRAPPER}}:hover"}]}, "blockMargin": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { margin: {{VALUE}}; }"}]}, "blockPadding": {"type": "object", "default": {"dimension": 1, "desktop": {"lock": true}}, "style": [{"selector": "{{WRAPPER}} { padding: {{VALUE}}; }"}]}}, "supports": {"inserter": false, "reusable": false, "html": false, "lock": false, "className": false, "customClassName": false}}
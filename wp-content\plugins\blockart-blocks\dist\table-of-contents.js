(()=>{const{$$:t,domReady:e,find:o,parseHTML:a,toArray:c,each:s,on:l}=window.blockartUtils,n=t=>{t.preventDefault();const e=t.target?.closest(".blockart-toc");"true"===e.getAttribute("data-collapsed")?e.setAttribute("data-collapsed","false"):e.setAttribute("data-collapsed","true")};e((()=>{(()=>{if(!o("#ba-smooth-scroll")){const t=Object.assign(document.createElement("style"),{id:"ba-smooth-scroll",innerHTML:"html {scroll-behavior: smooth;}"});document.head.appendChild(t)}})(),(()=>{const e=c(t(".blockart-toc"));s(e,(e=>{if(e.dataset.toc&&(e=>{if(e?.length)for(const c of e){const e=Array.from(t(`h${c.level}`)).find((t=>t.textContent===c.content));if(!e||o(e,`#${c.id.replace(/\d/g,(t=>"\\3"+t))}`))continue;const s=a(`<span id="${c.id}" class=".blockart-toc-anchor"></span>`,1);e?.insertAdjacentElement("afterbegin",s)}})(window?.[e.dataset.toc]),e.dataset.collapsed){const t=o(e,".blockart-toc-toggle");l("click",t,n)}}))})()}))})();